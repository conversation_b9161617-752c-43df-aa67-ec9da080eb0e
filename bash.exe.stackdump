Stack trace:
Frame         Function      Args
0007FFFF3440  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF2340) msys-2.0.dll+0x1FE8E
0007FFFF3440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3718) msys-2.0.dll+0x67F9
0007FFFF3440  000210046832 (000210286019, 0007FFFF32F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF3440  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3440  000210068E24 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3720  00021006A225 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCD50C0000 ntdll.dll
7FFCD4330000 KERNEL32.DLL
7FFCD2420000 KERNELBASE.dll
7FFCD4EB0000 USER32.dll
7FFCD2810000 win32u.dll
000210040000 msys-2.0.dll
7FFCD4B30000 GDI32.dll
7FFCD2D20000 gdi32full.dll
7FFCD2840000 msvcp_win.dll
7FFCD2BD0000 ucrtbase.dll
7FFCD4A70000 advapi32.dll
7FFCD4E00000 msvcrt.dll
7FFCD44F0000 sechost.dll
7FFCD35B0000 RPCRT4.dll
7FFCD1810000 CRYPTBASE.DLL
7FFCD2B30000 bcryptPrimitives.dll
7FFCD4480000 IMM32.DLL
