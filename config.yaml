development:
  machines:
    Machine_localhost_50000:
      monitoring:
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
        subscription_interval_ms: 1000
      nodes:
        '762907877': ns=1;g=479f390c-12db-4a9e-ab81-d985f1dadd60
        '762907878': ns=1;g=19847a36-212c-4047-89eb-62ec1b799475
        '762907879': ns=1;g=c9a860be-cdd3-468f-9d64-ec15513bbf4d
        bp_connectivity_command: ns=2;s=BP."BP Connectivity.Command"
        bp_connectivity_feedback: ns=2;s=BP."BP Connectivity.Feedback"
        bp_connectivity_state: ns=2;s=BP."BP Connectivity.State"
        briefstatus: ns=2;s=BP.Device.BriefStatus
        currentkeepalivecount: ns=1;g=03a6ad12-1547-4a6f-9c49-83e624012f58
        currentkeepalivecount_1: ns=1;g=e4e864a0-80eb-4c38-be78-df9bf34a5450
        currentkeepalivecount_2: ns=1;g=a316252b-5815-428c-a1d2-c167165bebff
        currentlifetimecount: ns=1;g=dd929238-fa5a-4b3e-a6a6-0ff843e45df3
        currentlifetimecount_1: ns=1;g=ac4154be-ed73-4426-beb5-4305751c8318
        currentlifetimecount_2: ns=1;g=7f4a5f6d-4fca-4c87-9c79-de2a8b9ffca2
        datachangenotificationscount: ns=1;g=b5c4a60d-ce45-491d-bc6b-cb7f5d6e6acd
        datachangenotificationscount_1: ns=1;g=c5785a70-03fa-4798-81d8-952edb43e1ab
        datachangenotificationscount_2: ns=1;g=1020176a-b990-48ea-8df5-5a2092503afa
        detailedstatus: ns=2;s=BP.Device.DetailedStatus
        disablecount: ns=1;g=759b5878-2350-4177-b207-90801cb0ed85
        disablecount_1: ns=1;g=4dfe58e6-a568-4b09-9512-d5fe5479c383
        disablecount_2: ns=1;g=bfbe87db-c736-42d8-8b85-0be9805d3bbc
        disabledmonitoreditemcount: ns=1;g=af0a314f-5f49-46ee-b63b-4f5d12be248d
        disabledmonitoreditemcount_1: ns=1;g=31a7fcb8-8a41-420d-9f8d-c4b9ca1e5f61
        disabledmonitoreditemcount_2: ns=1;g=ab6c9c5e-53a8-4a46-82d4-4655ea4c3983
        disableflush: ns=1;g=95c50615-4064-4480-9985-760e59533772
        discardedmessagecount: ns=1;g=8ad0ac3b-c230-4f5b-909c-351b44492097
        discardedmessagecount_1: ns=1;g=0c1d2044-a7d3-4f86-94e0-4c47f61a39dd
        discardedmessagecount_2: ns=1;g=39085ddc-b01d-483a-b037-4a00af3f7de0
        enablecount: ns=1;g=9b4ca6ca-121f-49ed-9dc0-70c9f63e1144
        enablecount_1: ns=1;g=faf63a4c-52a1-4a08-b3f8-85cca613b3e7
        enablecount_2: ns=1;g=5d1b691d-2e7f-472e-9bde-403d4653eda0
        endtime: ns=2;s=BP.Build.EndTime
        enumstrings: ns=2;s=Scan System.ScanField 1."ScanField 1.Status".EnumStrings
        enumstrings_1: ns=2;s=Scan System.ScanField 1."ScanField 1.Command".EnumStrings
        enumstrings_10: ns=2;s=Trigger Service."Trigger Service.Status".EnumStrings
        enumstrings_11: ns=2;s=Trigger Service."Trigger Service.Command".EnumStrings
        enumstrings_12: ns=2;s=Persistency Service."Persistency Service.Status".EnumStrings
        enumstrings_13: ns=2;s=Persistency Service."Persistency Service.Command".EnumStrings
        enumstrings_14: ns=2;s=Fpga Logging Service."Fpga Logging Service.Status".EnumStrings
        enumstrings_15: ns=2;s=Fpga Logging Service."Fpga Logging Service.Command".EnumStrings
        enumstrings_16: ns=2;s=Training Script Service."Training Script Service.Status".EnumStrings
        enumstrings_17: ns=2;s=Training Script Service."Training Script Service.Command".EnumStrings
        enumstrings_18: ns=2;s=Scan System Wrapper Service."Scan System Wrapper Service.Status".EnumStrings
        enumstrings_19: ns=2;s=Scan System Wrapper Service."Scan System Wrapper Service.Command".EnumStrings
        enumstrings_2: ns=2;s=Scan System."Scan System.Status".EnumStrings
        enumstrings_3: ns=2;s=Scan System."Scan System.Command".EnumStrings
        enumstrings_4: ns=2;s=Script Service."Script Service.Status".EnumStrings
        enumstrings_5: ns=2;s=Script Service."Script Service.Command".EnumStrings
        enumstrings_6: ns=2;s=Logging Service."Logging Service.Status".EnumStrings
        enumstrings_7: ns=2;s=Logging Service."Logging Service.Command".EnumStrings
        enumstrings_8: ns=2;s=Manager Service."Manager Service.Status".EnumStrings
        enumstrings_9: ns=2;s=Manager Service."Manager Service.Command".EnumStrings
        estimatedduration: ns=2;s=BP.Build.EstimatedDuration
        estimatedendtime: ns=2;s=BP.Build.EstimatedEndTime
        eventnotificationscount: ns=1;g=09f4bcd5-987e-4b62-8970-9634b97bb96b
        eventnotificationscount_1: ns=1;g=166ab2cf-4a0e-4255-8536-5a7c4c93b9a8
        eventnotificationscount_2: ns=1;g=c7934ba9-951a-4d85-9535-e93b496fbab0
        eventqueueoverflowcount: ns=1;g=b56b2f14-5c67-4e6b-8cc8-a9ebb25df559
        eventqueueoverflowcount_1: ns=1;g=a6acba27-194d-49fa-98b1-ce25cae593cc
        eventqueueoverflowcount_2: ns=1;g=4d1562e1-6788-4834-b3f5-dc6a40ca2dec
        fpga_logging_command: ns=2;s=Fpga Logging Service."Fpga Logging.Command"
        fpga_logging_enable: ns=2;s=Fpga Logging Service."Fpga Logging.Enable"
        fpga_logging_feedback: ns=2;s=Fpga Logging Service."Fpga Logging.Feedback"
        fpga_logging_list: ns=2;s=Fpga Logging Service."Fpga Logging.List"
        fpga_logging_service_command: ns=2;s=Fpga Logging Service."Fpga Logging Service.Command"
        fpga_logging_service_line_number: ns=2;s=Fpga Logging Service."Fpga Logging
          Service.Line Number"
        fpga_logging_service_script_error: ns=2;s=Fpga Logging Service."Fpga Logging
          Service.Script Error"
        fpga_logging_service_script_name: ns=2;s=Fpga Logging Service."Fpga Logging
          Service.Script Name"
        fpga_logging_service_status: ns=2;s=Fpga Logging Service."Fpga Logging Service.Status"
        fpga_logging_state: ns=2;s=Fpga Logging Service."Fpga Logging.State"
        id: ns=2;s=BP.Build.Id
        index: ns=2;s=BP.Build.CurrentLayer.Index
        inputarguments: ns=1;g=20d890bf-ecf5-4403-b0d4-c1957408b3ed
        inputarguments_1: ns=1;g=bd34fa80-720c-45a9-8c87-1da0604c4690
        inputarguments_2: ns=1;g=381ba731-f9b8-4ec2-843f-37f3227d7505
        inputarguments_3: ns=1;g=9fb5d257-45f5-4402-8006-3c5c6d4ed5fe
        inputarguments_4: ns=1;g=02100f26-14bd-4f6d-9131-7e66e8013249
        isactive: ns=1;g=8c066ae0-6fba-4e10-8b9b-4929814175d8
        laser_1_control_feedback: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Control
          Feedback"
        laser_1_control_request: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Control
          Request"
        laser_1_diameter_feedback: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Diameter
          Feedback"
        laser_1_diameter_request: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Diameter
          Request"
        laser_1_power_feedback: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Power
          Feedback"
        laser_1_power_request: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Power
          Request"
        laser_1_status_feedback: ns=2;s=Scan System.ScanField 1.Laser 1."Laser 1.Status
          Feedback"
        latepublishrequestcount: ns=1;g=f634afff-c657-4624-a68d-c1fc2d426eaa
        latepublishrequestcount_1: ns=1;g=8bdc7e64-7987-4810-b8ea-e6257c38ce6b
        latepublishrequestcount_2: ns=1;g=42f5995d-b0dc-4374-8a99-72bdb231994b
        logchannel: ns=2;s=Logging Service.LogChannel
        loggercommand: ns=2;s=Logging Service.LoggerCommand
        loggerpath: ns=2;s=Logging Service.LoggerPath
        loggerstatus: ns=2;s=Logging Service.LoggerStatus
        logging_service_command: ns=2;s=Logging Service."Logging Service.Command"
        logging_service_line_number: ns=2;s=Logging Service."Logging Service.Line
          Number"
        logging_service_script_error: ns=2;s=Logging Service."Logging Service.Script
          Error"
        logging_service_script_name: ns=2;s=Logging Service."Logging Service.Script
          Name"
        logging_service_status: ns=2;s=Logging Service."Logging Service.Status"
        manager_service_command: ns=2;s=Manager Service."Manager Service.Command"
        manager_service_line_number: ns=2;s=Manager Service."Manager Service.Line
          Number"
        manager_service_script_error: ns=2;s=Manager Service."Manager Service.Script
          Error"
        manager_service_script_name: ns=2;s=Manager Service."Manager Service.Script
          Name"
        manager_service_status: ns=2;s=Manager Service."Manager Service.Status"
        manufacturer: ns=2;s=BP.Device.Manufacturer
        maxbackupfiles: ns=1;g=699a3a99-7737-438f-84c6-3d27a12f8c5a
        maxentries: ns=1;g=5936a912-4dac-48b1-ab12-9da861175ae8
        maxkeepalivecount: ns=1;g=200eb2e6-1916-40b9-9ea6-4e2f61ba6651
        maxkeepalivecount_1: ns=1;g=6e9edae7-a14e-4a16-b05a-2d4387c94a1c
        maxkeepalivecount_2: ns=1;g=e3cd1e87-bb0d-4fab-acb9-491b69e162b8
        maxlifetimecount: ns=1;g=75e59953-ebe8-481c-b706-01e28483a7bf
        maxlifetimecount_1: ns=1;g=6f52de41-ad41-4ef4-80a7-d61860326f59
        maxlifetimecount_2: ns=1;g=72aff477-5fa4-4063-a287-6a9f047634b9
        maxnotificationsperpublish: ns=1;g=266a5a9f-91f4-4eea-b478-4f12120e8fef
        maxnotificationsperpublish_1: ns=1;g=69e8c24c-0e00-4a13-9ff4-8a979f197211
        maxnotificationsperpublish_2: ns=1;g=3300f011-5da9-436a-aae5-102dc3c64783
        mcp_interface_layout: ns=2;s=User Channels."MCP Interface.Layout"
        modifycount: ns=1;g=474801d3-1af4-4766-8f07-99b359203f49
        modifycount_1: ns=1;g=8457b28a-2753-4ca0-b389-c7c104d8c9c5
        modifycount_2: ns=1;g=eaeb075d-60ca-4e10-b353-0e008d32d7de
        monitoreditemcount: ns=1;g=e4fb9dc0-591d-4295-b509-f35b48e0e9eb
        monitoreditemcount_1: ns=1;g=ddb2fcbe-0c95-42e7-8fce-e969cadcb74c
        monitoreditemcount_2: ns=1;g=46768a26-a5cc-4e86-9737-d47e45aca96c
        monitoreditemdata: ns=1;s=ObjectStatistics_MonitoredItemData
        monitoreditemevent: ns=1;s=ObjectStatistics_MonitoredItemEvent
        monitoringqueueoverflowcount: ns=1;g=9d46ff61-1850-4580-847b-87ec78d040ca
        monitoringqueueoverflowcount_1: ns=1;g=53e007c9-0665-43d3-add8-d0972a5dfbe1
        monitoringqueueoverflowcount_2: ns=1;g=113d20d5-6837-4f5c-99af-15344e12c03a
        mtls_0004_1_d0_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.D0 Setpoint"
        mtls_0004_1_d1_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.D1 Setpoint"
        mtls_0004_1_datablock_info: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Datablock
          Info"
        mtls_0004_1_laser0_enable: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Laser0
          Enable"
        mtls_0004_1_laser1_enable: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Laser1
          Enable"
        mtls_0004_1_laser_status: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Laser Status"
        mtls_0004_1_module_status: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Module
          Status"
        mtls_0004_1_p0_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.P0 Setpoint"
        mtls_0004_1_p1_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.P1 Setpoint"
        mtls_0004_1_v_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.V Setpoint"
        mtls_0004_1_x_feedback: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.X Feedback"
        mtls_0004_1_x_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.X Setpoint"
        mtls_0004_1_y_feedback: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Y Feedback"
        mtls_0004_1_y_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Y Setpoint"
        mtls_0004_1_z0_feedback: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Z0 Feedback"
        mtls_0004_1_z0_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Z0 Setpoint"
        mtls_0004_1_z1_feedback: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Z1 Feedback"
        mtls_0004_1_z1_setpoint: ns=2;s=Target_0.MTLS-0004 1."MTLS-0004 1.Z1 Setpoint"
        name: ns=2;s=BP.Device.Name
        nextsequencenumber: ns=1;g=9a496270-f282-428f-87b8-9dfb7b8c0268
        nextsequencenumber_1: ns=1;g=75edacc6-cd8f-408b-ac81-265c321b9a54
        nextsequencenumber_2: ns=1;g=1cc0ad8e-053c-4b1b-9ff1-10365c1d27b6
        notificationscount: ns=1;g=24355c46-ed69-450a-b1bb-42a66450aaa6
        notificationscount_1: ns=1;g=99094e68-a738-4230-8b48-4477c84fd33f
        notificationscount_2: ns=1;g=09636b8b-7fe9-4e09-b174-72617df2dfa7
        numberoflayers: ns=2;s=BP.Build.NumberOfLayers
        persistency_service_command: ns=2;s=Persistency Service."Persistency Service.Command"
        persistency_service_line_number: ns=2;s=Persistency Service."Persistency Service.Line
          Number"
        persistency_service_script_error: ns=2;s=Persistency Service."Persistency
          Service.Script Error"
        persistency_service_script_name: ns=2;s=Persistency Service."Persistency Service.Script
          Name"
        persistency_service_status: ns=2;s=Persistency Service."Persistency Service.Status"
        persistencymanager_command: ns=2;s=Persistency Service."PersistencyManager.Command"
        persistencymanager_feedback: ns=2;s=Persistency Service."PersistencyManager.Feedback"
        persistencymanager_state: ns=2;s=Persistency Service."PersistencyManager.State"
        priority: ns=1;g=9fa52cf7-4d67-4cb4-9973-8bcf969739af
        priority_1: ns=1;g=6614ca92-4e48-43bc-a7f7-b8d9f9032a1d
        priority_2: ns=1;g=07fa68b9-5290-4403-b3de-dc2d6624f1ca
        progress: ns=2;s=BP.Build.Progress
        publishingenabled: ns=1;g=2e924eae-c9bb-4788-b658-7db3d0de2b27
        publishingenabled_1: ns=1;g=55dc2237-561c-45fd-b2cb-6d2486e5945d
        publishingenabled_2: ns=1;g=8a05b590-3974-407c-a071-0f4202b09629
        publishinginterval: ns=1;g=09e9df45-0de5-479e-9bc8-92d379557fbe
        publishinginterval_1: ns=1;g=f1ecf374-f26c-4bb6-b58e-6f2eb0e2cad7
        publishinginterval_2: ns=1;g=b6876737-e2b5-4e9b-88f4-a90d37408b8f
        publishrequestcount: ns=1;g=49c54027-b459-431e-b5e8-e222280ae7c4
        publishrequestcount_1: ns=1;g=100aa1eb-d079-4bde-88fa-52b64399b279
        publishrequestcount_2: ns=1;g=5976c7d8-2f7e-477b-8c52-934efb4479bf
        republishmessagecount: ns=1;g=723c886a-2668-4c6a-82fa-e47776de1614
        republishmessagecount_1: ns=1;g=9e454962-6d7b-47fa-8f3d-3705e0e4f00a
        republishmessagecount_2: ns=1;g=23d8513f-726c-4c84-9778-0188972989b9
        republishmessagerequestcount: ns=1;g=3ae0a528-8d6a-4052-94f0-d8e908ce303d
        republishmessagerequestcount_1: ns=1;g=35b0d69b-964a-449d-b781-72e98f98932e
        republishmessagerequestcount_2: ns=1;g=f2724db9-d740-43ee-9f11-1dd13a360ec1
        republishrequestcount: ns=1;g=66cd441d-c12f-4878-87b0-cb1fd6d454c7
        republishrequestcount_1: ns=1;g=c9b7e275-7c4a-4208-b1d1-d30e842fe3f1
        republishrequestcount_2: ns=1;g=f1253b2b-5fc8-455e-9d42-4941c068536f
        runid: ns=2;s=BP.Build.RunId
        scan_system_command: ns=2;s=Scan System."Scan System.Command"
        scan_system_height: ns=2;s=Scan System."Scan System.Current Height"
        scan_system_jobfile: ns=2;s=Scan System."Scan System.JobFile"
        scan_system_jobid: ns=2;s=Scan System."Scan System.JobID"
        scan_system_layer: ns=2;s=Scan System."Scan System.Current Layer"
        scan_system_remaining_scan_time: ns=2;s=Scan System."Scan System.Remaining
          Scan Time"
        scan_system_status: ns=2;s=Scan System."Scan System.Status"
        scan_system_total_layers: ns=2;s=Scan System."Scan System.Total Layers"
        scan_system_wrapper_command: ns=2;s=Scan System Wrapper Service."Scan System
          Wrapper.Command"
        scan_system_wrapper_feedback: ns=2;s=Scan System Wrapper Service."Scan System
          Wrapper.Feedback"
        scan_system_wrapper_list: ns=2;s=Scan System Wrapper Service."Scan System
          Wrapper.List"
        scan_system_wrapper_service_command: ns=2;s=Scan System Wrapper Service."Scan
          System Wrapper Service.Command"
        scan_system_wrapper_service_line_number: ns=2;s=Scan System Wrapper Service."Scan
          System Wrapper Service.Line Number"
        scan_system_wrapper_service_script_error: ns=2;s=Scan System Wrapper Service."Scan
          System Wrapper Service.Script Error"
        scan_system_wrapper_service_script_name: ns=2;s=Scan System Wrapper Service."Scan
          System Wrapper Service.Script Name"
        scan_system_wrapper_service_status: ns=2;s=Scan System Wrapper Service."Scan
          System Wrapper Service.Status"
        scan_system_wrapper_state: ns=2;s=Scan System Wrapper Service."Scan System
          Wrapper.State"
        scanfield_1_command: ns=2;s=Scan System.ScanField 1."ScanField 1.Command"
        scanfield_1_datablock_info: ns=2;s=Scan System.ScanField 1."ScanField 1.Datablock
          info"
        scanfield_1_status: ns=2;s=Scan System.ScanField 1."ScanField 1.Status"
        scanfield_1_v_feedback: ns=2;s=Scan System.ScanField 1."ScanField 1.V Feedback"
        scanfield_1_v_request: ns=2;s=Scan System.ScanField 1."ScanField 1.V Request"
        scanfield_1_x_feedback: ns=2;s=Scan System.ScanField 1."ScanField 1.X Feedback"
        scanfield_1_x_request: ns=2;s=Scan System.ScanField 1."ScanField 1.X Request"
        scanfield_1_y_feedback: ns=2;s=Scan System.ScanField 1."ScanField 1.Y Feedback"
        scanfield_1_y_request: ns=2;s=Scan System.ScanField 1."ScanField 1.Y Request"
        scanfield_1_z0_feedback: ns=2;s=Scan System.ScanField 1."ScanField 1.Z0 Feedback"
        scanfield_1_z0_request: ns=2;s=Scan System.ScanField 1."ScanField 1.Z0 Request"
        scanfield_1_z1_feedback: ns=2;s=Scan System.ScanField 1."ScanField 1.Z1 Feedback"
        scanfield_1_z1_request: ns=2;s=Scan System.ScanField 1."ScanField 1.Z1 Request"
        script_service_command: ns=2;s=Script Service."Script Service.Command"
        script_service_line_number: ns=2;s=Script Service."Script Service.Line Number"
        script_service_script_error: ns=2;s=Script Service."Script Service.Script
          Error"
        script_service_script_name: ns=2;s=Script Service."Script Service.Script Name"
        script_service_status: ns=2;s=Script Service."Script Service.Status"
        securechannel: ns=1;s=ObjectStatistics_SecureChannel
        securechannelcumulated: ns=1;s=ObjectStatistics_SecureChannelCumulated
        servicemanager_command: ns=2;s=Manager Service."ServiceManager.Command"
        servicemanager_feedback: ns=2;s=Manager Service."ServiceManager.Feedback"
        servicemanager_list: ns=2;s=Manager Service."ServiceManager.List"
        session: ns=1;s=ObjectStatistics_Session
        sessionid: ns=1;g=63616486-5fa8-4fcd-804f-3f77c1804d6d
        sessionid_1: ns=1;g=2a14ab43-0b9b-42e4-af2b-881a2f014f96
        sessionid_2: ns=1;g=d0df4c71-220a-44a1-8a7c-07a0c3508c41
        stacktraceactive: ns=1;g=a29d8c94-ce2c-4559-9915-f67768614416
        stacktracelevel: ns=1;g=beab1aa8-457f-4a70-bd0a-830fff7cb04f
        starttime: ns=2;s=BP.Build.StartTime
        starttime_1: ns=2;s=BP.Device.StartTime
        status: ns=2;s=BP.Device.Status
        subscription: ns=1;s=ObjectStatistics_Subscription
        subscriptionid: ns=1;g=43942a38-f9d1-472a-a7af-6cc854fa5fa8
        subscriptionid_1: ns=1;g=2d66c86c-6e81-4f24-9ebb-a3508a447b64
        subscriptionid_2: ns=1;g=a632a4f8-33b2-488d-9b81-7f1ee3bae16f
        system_monitor_cpu: ns=2;s=System Monitor."System Monitor.CPU"
        system_monitor_disk_usage: ns=2;s=System Monitor."System Monitor.Disk Usage"
        system_monitor_emergency_stop: ns=2;s=System Monitor."System Monitor.Emergency
          Stop"
        system_monitor_error_log: ns=2;s=System Monitor."System Monitor.Error Log"
        system_monitor_physical_memory: ns=2;s=System Monitor."System Monitor.Physical
          Memory"
        system_monitor_system_time: ns=2;s=System Monitor."System Monitor.System Time"
        system_monitor_virtual_memory: ns=2;s=System Monitor."System Monitor.Virtual
          Memory"
        system_monitor_watch_dog: ns=2;s=System Monitor."System Monitor.Watch Dog"
        traceeventlevel: ns=1;g=9d15fb6b-818f-421a-b822-bdcf8963e029
        tracefile: ns=1;g=7cc13418-4d3b-4227-ad93-3b6f33da5a5b
        tracelevel: ns=1;g=4c3aca48-9c72-4eca-b25f-95ed146b6c17
        training_led1: ns=2;s=User Channels.Training."Training.Led1"
        training_led2: ns=2;s=User Channels.Training."Training.Led2"
        training_led3: ns=2;s=User Channels.Training."Training.Led3"
        training_led4: ns=2;s=User Channels.Training."Training.Led4"
        training_led5: ns=2;s=User Channels.Training."Training.Led5"
        training_led6: ns=2;s=User Channels.Training."Training.Led6"
        training_led7: ns=2;s=User Channels.Training."Training.Led7"
        training_led8: ns=2;s=User Channels.Training."Training.Led8"
        training_potentiometer1: ns=2;s=User Channels.Training."Training.Potentiometer1"
        training_potentiometer2: ns=2;s=User Channels.Training."Training.Potentiometer2"
        training_potentiometer3: ns=2;s=User Channels.Training."Training.Potentiometer3"
        training_script_command: ns=2;s=Training Script Service."Training Script.Command"
        training_script_command_list: ns=2;s=Training Script Service."Training Script.Command
          List"
        training_script_feedback: ns=2;s=Training Script Service."Training Script.Feedback"
        training_script_service_command: ns=2;s=Training Script Service."Training
          Script Service.Command"
        training_script_service_line_number: ns=2;s=Training Script Service."Training
          Script Service.Line Number"
        training_script_service_script_error: ns=2;s=Training Script Service."Training
          Script Service.Script Error"
        training_script_service_script_name: ns=2;s=Training Script Service."Training
          Script Service.Script Name"
        training_script_service_status: ns=2;s=Training Script Service."Training Script
          Service.Status"
        training_script_state: ns=2;s=Training Script Service."Training Script.State"
        training_switch1: ns=2;s=User Channels.Training."Training.Switch1"
        training_switch2: ns=2;s=User Channels.Training."Training.Switch2"
        training_switch3: ns=2;s=User Channels.Training."Training.Switch3"
        training_switch4: ns=2;s=User Channels.Training."Training.Switch4"
        training_switch5: ns=2;s=User Channels.Training."Training.Switch5"
        training_switch6: ns=2;s=User Channels.Training."Training.Switch6"
        training_switch7: ns=2;s=User Channels.Training."Training.Switch7"
        training_switch8: ns=2;s=User Channels.Training."Training.Switch8"
        transferredtoaltclientcount: ns=1;g=4deccefe-01e1-4818-a64c-2faed9103676
        transferredtoaltclientcount_1: ns=1;g=fa66f443-ea10-40ff-a36e-46299e6e6f76
        transferredtoaltclientcount_2: ns=1;g=268caa9e-eebe-4bfe-bd44-a5c051379e7b
        transferredtosameclientcount: ns=1;g=b3154e38-e6b4-43a3-a228-86d79f15f167
        transferredtosameclientcount_1: ns=1;g=45bf8828-801c-4a13-99e9-5f5b57b8e306
        transferredtosameclientcount_2: ns=1;g=1a0c89c9-fb67-46f6-941d-fd3ba1477f42
        transferrequestcount: ns=1;g=e31326b1-0878-4b61-b920-bd88899254a8
        transferrequestcount_1: ns=1;g=3a8106de-0260-4e9e-8912-1a8389d7af73
        transferrequestcount_2: ns=1;g=b7f647c5-cb51-4dd6-b226-0432d1bf1f05
        trigger_command: ns=2;s=Trigger Service."Trigger.Command"
        trigger_continueaftererror: ns=2;s=Trigger Service."Trigger.ContinueAfterError"
        trigger_dryrun: ns=2;s=Trigger Service."Trigger.DryRun"
        trigger_feedback: ns=2;s=Trigger Service."Trigger.Feedback"
        trigger_list: ns=2;s=Trigger Service."Trigger.List"
        trigger_scanning: ns=2;s=Trigger Service."Trigger.Scanning"
        trigger_service_command: ns=2;s=Trigger Service."Trigger Service.Command"
        trigger_service_line_number: ns=2;s=Trigger Service."Trigger Service.Line
          Number"
        trigger_service_script_error: ns=2;s=Trigger Service."Trigger Service.Script
          Error"
        trigger_service_script_name: ns=2;s=Trigger Service."Trigger Service.Script
          Name"
        trigger_service_status: ns=2;s=Trigger Service."Trigger Service.Status"
        trigger_state: ns=2;s=Trigger Service."Trigger.State"
        uadatatype: ns=1;s=ObjectStatistics_UaDataType
        uamethod: ns=1;s=ObjectStatistics_UaMethod
        uamutex: ns=1;s=ObjectStatistics_UaMutex
        uanode: ns=1;s=ObjectStatistics_UaNode
        uaobject: ns=1;s=ObjectStatistics_UaObject
        uaobjecttype: ns=1;s=ObjectStatistics_UaObjectType
        uareferencetype: ns=1;s=ObjectStatistics_UaReferenceType
        uavariable: ns=1;s=ObjectStatistics_UaVariable
        uavariabletype: ns=1;s=ObjectStatistics_UaVariableType
        uaview: ns=1;s=ObjectStatistics_UaView
        unacknowledgedmessagecount: ns=1;g=971ccebf-7296-4289-be11-3112daa71447
        unacknowledgedmessagecount_1: ns=1;g=a339d12f-82ec-44dd-ab32-db2942375941
        unacknowledgedmessagecount_2: ns=1;g=43caff63-f1f6-44c1-949d-340c9426892e
        uploadid: ns=2;s=BP.Build.UploadId
      opcua:
        authentication:
          enabled: true
          password: pw
          username: Win11
        security:
          enabled: true
          policy: Basic256Sha256
          cert_file: null  # Will auto-generate if not provided
          private_key_file: null  # Will auto-generate if not provided
        namespaces:
        - 1
        - 2
        url: opc.tcp://localhost:4840
      type: unknown
