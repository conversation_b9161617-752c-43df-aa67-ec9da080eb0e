# Class Diagram - MicroSLM Monitoring System

```mermaid
classDiagram
    class MainApplication {
        -log: Logger
        +initialize_application() tuple
        +load_application_config() dict
        +validate_influxdb_config(config) bool
        +main() None
    }

    class ConfigLoader {
        +load_config(path: str) dict
    }

    class LoggerSetup {
        +setup_logger(log_level, log_file) Logger
        +get_console_handler() StreamHandler
        +get_file_handler(log_file) TimedRotatingFileHandler
    }

    class InfluxWriter {
        -url: str
        -token: str
        -org: str
        -bucket: str
        -client: InfluxDBClient
        -writer: Write<PERSON>pi
        +__init__(influx_config: dict)
        +write(measurement: str, fields: dict, tags: dict, timestamp: datetime) None
    }

    class OpcuaMonitor {
        +monitor_opcua_machine(machine_name: str, machine_config: dict, influx_writer: InfluxWriter) None
    }

    class OpcuaDataHandler {
        -influx_writer: InfluxWriter
        -machine_name: str
        -id_to_name: dict
        -static_tags: dict
        +__init__(influx_writer, machine_name, id_to_name, static_tags)
        +datachange_notification(node: Node, val, data: DataChangeNotif) None
    }

    class ModbusMonitor {
        -machine_name: str
        -modbus_config: dict
        -influx_writer: InfluxWriter
        -static_tags: dict
        -counters: dict
        +__init__(machine_name: str, machine_config: dict, influx_writer: InfluxWriter)
        +monitor() None
        +_read_registers() dict
        +_read_register(register_config: dict) any
        +_should_log_success() bool
    }

    class OPCUANodeDiscoverer {
        -server_url: str
        -timeout: int
        -max_depth: int
        -discovered_nodes: dict
        -client: Client
        +__init__(server_url: str, timeout: int, max_depth: int)
        +connect_to_server() bool
        +disconnect_from_server() None
        +recursive_browse(node: Node, depth: int, path: str) None
        +discover_nodes() dict
        +generate_machine_config(machine_name: str, machine_type: str) dict
        +save_config_to_file(config: dict, filename: str) None
    }

    %% Relationships
    MainApplication --> ConfigLoader : uses
    MainApplication --> LoggerSetup : uses
    MainApplication --> InfluxWriter : creates
    MainApplication --> OpcuaMonitor : creates tasks
    MainApplication --> ModbusMonitor : creates tasks

    OpcuaMonitor --> InfluxWriter : uses
    OpcuaMonitor --> OpcuaDataHandler : creates
    OpcuaDataHandler --> InfluxWriter : writes data

    ModbusMonitor --> InfluxWriter : uses

    %% Configuration Generation Flow
    OPCUANodeDiscoverer --> ConfigLoader : generates config for
    OPCUANodeDiscoverer --> AsyncuaClient : uses for discovery

    %% External Dependencies
    class InfluxDBClient {
        <<external>>
    }
    
    class AsyncuaClient {
        <<external>>
    }
    
    class ModbusClient {
        <<external>>
    }

    InfluxWriter --> InfluxDBClient : uses
    OpcuaMonitor --> AsyncuaClient : uses
    ModbusMonitor --> ModbusClient : uses

    %% Configuration Classes
    class MachineConfig {
        +type: str
        +protocol: str
        +opcua: dict
        +modbus: dict
        +nodes: dict
        +monitoring: dict
    }

    class InfluxConfig {
        +url: str
        +token: str
        +org: str
        +bucket: str
        +batch_size: int
        +flush_interval_seconds: int
    }

    MainApplication --> MachineConfig : reads
    MainApplication --> InfluxConfig : reads
    InfluxWriter --> InfluxConfig : uses
    OpcuaMonitor --> MachineConfig : uses
    ModbusMonitor --> MachineConfig : uses
```
