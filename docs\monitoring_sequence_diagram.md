# Manufacturing Monitoring System - Sequence Diagram

This document shows how the manufacturing monitoring system components interact during the complete monitoring process after the config.yaml file is available.

## Complete Monitoring Flow Sequence Diagram

```mermaid
sequenceDiagram
    participant User as User/System
    participant Main as main.py
    participant Startup as startup module
    participant Logger as logger.py
    participant Config as config.py
    participant <PERSON><PERSON><PERSON> as config_validator.py
    participant InfluxWriter as InfluxWriter
    participant InfluxDB as InfluxDB Server
    participant OPCUAMonitor as opcua_monitor.py
    participant OPCUAServer as OPC UA Server
    participant FileSystem as File System

    Note over User: User starts application: python main.py
    
    User->>Main: Start application
    Main->>Main: Import modules and initialize global log
    
    %% Step 1: Initialize Logging System
    Main->>Startup: initialize_logging()
    Startup->>Logger: setup_logger(log_level, log_file)
    Logger->>FileSystem: Create logs directory
    FileSystem->>Logger: Directory created
    Logger->>Logger: Setup console and file handlers
    Logger->>Logger: Configure log rotation (daily, 28 backups)
    Logger->>Startup: Logger instance
    Startup->>Startup: Generate timestamped log filename
    Startup->>Main: (logger, None)
    
    %% Step 2: Load and Validate Configuration
    Main->>Startup: load_and_validate_config()
    Startup->>Config: load_yaml_config("config.yaml")
    Config->>FileSystem: Check if config.yaml exists
    FileSystem->>Config: File exists
    Config->>FileSystem: Read config.yaml
    FileSystem->>Config: YAML content
    Config->>Config: Parse YAML with safe_load()
    Config->>Startup: config dictionary
    
    Startup->>Validator: validate_config(config)
    Validator->>Validator: Check required sections (development/production)
    Validator->>Validator: Validate InfluxDB configuration
    Validator->>Validator: Validate machine configurations
    Validator->>Startup: Validation successful
    Startup->>Main: config dictionary
    
    %% Step 3: Environment Detection and Setup
    Main->>Main: Get ENVIRONMENT variable (default: 'development')
    Main->>Main: Extract environment-specific config
    Main->>Main: Log environment mode
    
    %% Step 4: Initialize InfluxDB Writer
    Main->>InfluxWriter: InfluxWriter(env_config['influxdb'])
    InfluxWriter->>InfluxWriter: Extract connection parameters (url, token, org, bucket)
    InfluxWriter->>InfluxDB: Create client connection
    InfluxDB->>InfluxWriter: Client instance
    InfluxWriter->>InfluxDB: Test connection with ping()
    InfluxDB->>InfluxWriter: Connection successful
    InfluxWriter->>InfluxWriter: Create asynchronous write API
    InfluxWriter->>Main: InfluxWriter instance
    Main->>Main: Log successful InfluxDB initialization
    
    %% Step 5: Machine Monitoring Setup
    Main->>Main: Extract machines configuration
    Main->>Main: Log starting monitoring for N machines
    Main->>Main: Initialize empty monitoring_tasks list
    
    %% Create Monitoring Tasks for Each Machine
    loop For each machine in machines
        Main->>Main: Log creating monitoring task for machine
        Main->>Main: Verify protocol type is OPC UA
        
        Main->>Main: asyncio.create_task(monitor_opcua_machine(...))
        Main->>OPCUAMonitor: monitor_opcua_machine(machine_name, machine_config, influx_writer)
        Note over OPCUAMonitor: Task created
        OPCUAMonitor->>Main: Task reference
        Main->>Main: Add to monitoring_tasks
        Main->>Main: Log OPC UA task created
    end
    
    %% Step 6: Start All Monitoring Tasks
    Main->>Main: Log all tasks created successfully
    Main->>Main: Log "Manufacturing Monitor is now running..."
    Main->>Main: Log "Press Ctrl+C to stop"
    
    %% Start Monitoring Tasks (Happy Path)
    Main->>OPCUAMonitor: Start asyncio task execution
    
    OPCUAMonitor->>OPCUAMonitor: Extract OPC UA configuration
    OPCUAMonitor->>OPCUAMonitor: Extract monitoring parameters
    
    OPCUAMonitor->>OPCUAServer: Client(url, timeout) - Establish connection
    OPCUAServer->>OPCUAMonitor: Connection established
    OPCUAMonitor->>OPCUAMonitor: Log successful connection
    OPCUAMonitor->>OPCUAMonitor: Create node ID to name mapping
    OPCUAMonitor->>OPCUAMonitor: Setup static tags (type, name)
    OPCUAMonitor->>OPCUAMonitor: Create OpcuaDataHandler
    
    OPCUAMonitor->>OPCUAServer: Create subscription(period, handler)
    OPCUAServer->>OPCUAMonitor: Subscription instance
    
    OPCUAMonitor->>OPCUAMonitor: Build list of node objects from node IDs
    loop For each node ID
        OPCUAMonitor->>OPCUAServer: get_node(node_id)
        OPCUAServer->>OPCUAMonitor: Node object
        OPCUAMonitor->>OPCUAMonitor: Add to nodes_to_monitor
    end
    
    OPCUAMonitor->>OPCUAMonitor: Log subscribing to N nodes
    OPCUAMonitor->>OPCUAServer: subscription.subscribe_data_change(nodes_to_monitor)
    OPCUAServer->>OPCUAMonitor: Subscription active
    
    %% Continuous Data Collection
    loop Continuous monitoring
        OPCUAServer->>OPCUAMonitor: datachange_notification(node, value, data)
        OPCUAMonitor->>OPCUAMonitor: Extract node_id and map to field_name
        OPCUAMonitor->>OPCUAMonitor: Extract timestamp from data
        OPCUAMonitor->>OPCUAMonitor: Create fields dict {field_name: value}
        OPCUAMonitor->>InfluxWriter: write("machine_sensors", fields, tags, timestamp)
        InfluxWriter->>InfluxWriter: Create Point with measurement
        InfluxWriter->>InfluxWriter: Add tags and fields to point
        InfluxWriter->>InfluxWriter: Set timestamp
        InfluxWriter->>InfluxDB: Queue point for writing (async)
        InfluxDB->>InfluxWriter: Point queued successfully
        InfluxWriter->>OPCUAMonitor: Write completed
        OPCUAMonitor->>OPCUAMonitor: Log debug message about data change
    end
    
    %% Step 7: Normal Operation
    Main->>Main: asyncio.gather(*monitoring_tasks)
    Note over Main, InfluxDB: System runs continuously collecting and storing data
    Note over User: System operates normally until user stops it
    
    %% Step 8: Graceful Shutdown (when user stops)
    User->>Main: KeyboardInterrupt (Ctrl+C)
    Main->>Main: Log shutdown signal received
    Main->>Main: Log stopping monitoring tasks
    
    loop For each monitoring task
        Main->>Main: task.cancel()
    end
    
    Main->>Main: asyncio.gather(*monitoring_tasks, return_exceptions=True)
    Main->>Main: All tasks stopped
    Main->>Main: Log all monitoring tasks stopped
    Main->>Main: Monitoring complete
    
    %% Cleanup
    Main->>Main: Enter finally block
    Main->>InfluxWriter: close()
    InfluxWriter->>InfluxWriter: Try to close write API
    InfluxWriter->>InfluxDB: Flush pending writes
    InfluxDB->>InfluxWriter: Data flushed
    InfluxWriter->>InfluxDB: Close client connection
    InfluxDB->>InfluxWriter: Connection closed
    InfluxWriter->>Main: Cleanup complete
    Main->>Main: Log InfluxDB writer closed
    Main->>Main: Log "Manufacturing Monitor shutdown complete"
    Main->>Main: Log separator line
    Main->>User: Application exit (code 0)
```

## Component Responsibilities During Monitoring

### Main Application (`main.py`)
- **Purpose**: Application orchestrator and lifecycle manager
- **Key Functions**:
  - `main()`: Complete application lifecycle from startup to shutdown
  - `run_idle_service()`: Keep service running when no machines configured
  - Error handling and graceful shutdown coordination

### Startup Module (`startup/`)
- **Purpose**: Application initialization and configuration management
- **Key Functions**:
  - `initialize_logging()`: Setup logging infrastructure with file rotation
  - `load_and_validate_config()`: Load, parse, and validate YAML configuration
  - Environment setup and validation

### InfluxWriter (`influx_writer.py`)
- **Purpose**: Database connectivity and data persistence
- **Key Functions**:
  - `__init__()`: Establish and validate InfluxDB connection
  - `write()`: Asynchronous data point writing with error handling
  - `close()`: Graceful shutdown with data flushing

### OPC UA Monitor (`monitors/opcua_monitor.py`)
- **Purpose**: Real-time OPC UA data collection via subscriptions
- **Key Functions**:
  - `monitor_opcua_machine()`: Main monitoring loop with reconnection logic
  - `OpcuaDataHandler.datachange_notification()`: Handle incoming data changes
  - Automatic subscription management and node mapping

## Data Flow Patterns

### OPC UA Data Flow
1. **Subscription-Based**: Real-time notifications when data changes
2. **Event-Driven**: Data processing triggered by server notifications
3. **Timestamp Preservation**: Uses source timestamps from OPC UA server
4. **Field Mapping**: Node IDs mapped to clean field names via configuration

## Error Handling Strategies

### Connection Failures
- **Automatic Reconnection**: Exponential backoff retry logic
- **Connection Testing**: Validation before starting data collection
- **Graceful Degradation**: Continue monitoring other machines if one fails

### Data Collection Errors
- **Individual Error Isolation**: Single node failures don't stop collection from other nodes
- **Consecutive Error Tracking**: Disconnect after multiple consecutive failures
- **Comprehensive Logging**: Detailed error context for troubleshooting

### Database Errors
- **Asynchronous Writing**: Non-blocking database operations
- **Connection Validation**: Startup ping test to verify database connectivity
- **Graceful Shutdown**: Data flushing during application termination

## Performance Considerations

### Concurrency
- **Async/Await Pattern**: Non-blocking I/O for high performance
- **Parallel Machine Monitoring**: Independent tasks for each machine
- **Resource Efficiency**: Minimal thread usage with async operations

### Data Throughput
- **Batched Writes**: InfluxDB async write API with internal batching
- **Configurable Intervals**: Adjustable polling/subscription rates
- **Memory Management**: Streaming data processing without accumulation

### Scalability
- **Machine Independence**: Each machine runs in separate async task
- **Configuration-Driven**: Easy addition of new machines via YAML config

## Configuration Dependencies

### Required Configuration Structure
```yaml
environment:  # development or production
  machines:
    machine_name:
      type: equipment_type
      monitoring:
        connection_timeout_seconds: N
        retry_delay_seconds: N
        subscription_interval_ms: N
      opcua:
        url: server_endpoint
        namespaces: [list]
        authentication: {...}
      nodes: {...}     # OPC UA node mappings
  influxdb:
    url: database_url
    token: auth_token
    org: organization
    bucket: data_bucket
```

## Operational States

### Startup States
1. **Initializing**: Setting up logging and loading configuration
2. **Validating**: Testing database connectivity and config validation
3. **Starting**: Creating and launching monitoring tasks

### Running States
1. **Monitoring**: Active data collection and storage
2. **Reconnecting**: Handling connection failures with retry logic
3. **Idle**: Running with no configured machines (maintenance mode)

### Shutdown States
1. **Stopping**: Cancelling monitoring tasks gracefully
2. **Flushing**: Ensuring all data is written to database
3. **Cleanup**: Closing connections and releasing resources
