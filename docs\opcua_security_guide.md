# OPC UA Security and Authentication Guide

## Table of Contents
1. [Introduction to OPC UA Security](#introduction-to-opc-ua-security)
2. [Security Concepts](#security-concepts)
3. [Authentication Methods](#authentication-methods)
4. [Certificate-Based Security](#certificate-based-security)
5. [Security Policies](#security-policies)
6. [Implementation in Our System](#implementation-in-our-system)
7. [Practical Usage](#practical-usage)
8. [Troubleshooting](#troubleshooting)
9. [Best Practices](#best-practices)

## Introduction to OPC UA Security

OPC UA (Open Platform Communications Unified Architecture) is an industrial communication protocol that provides robust security features. Unlike older protocols, OPC UA was designed with security as a core requirement, not an afterthought.

### Why Security Matters in Industrial Systems
- **Data Integrity**: Ensure data hasn't been tampered with during transmission
- **Confidentiality**: Prevent unauthorized access to sensitive industrial data
- **Authentication**: Verify the identity of clients and servers
- **Authorization**: Control what authenticated users can access
- **Non-repudiation**: Ensure actions can be traced back to specific users

## Security Concepts

### 1. Security Modes
OPC UA defines three security modes:

#### None
- **Description**: No security applied
- **Use Case**: Development/testing environments only
- **Risk**: All data transmitted in plain text

#### Sign
- **Description**: Messages are digitally signed but not encrypted
- **Use Case**: When data integrity is important but confidentiality isn't critical
- **Protection**: Prevents tampering, but data is still readable

#### SignAndEncrypt
- **Description**: Messages are both signed and encrypted
- **Use Case**: Production environments with sensitive data
- **Protection**: Full confidentiality and integrity protection

### 2. Security Policies
Security policies define the cryptographic algorithms used:

#### None
- No cryptographic protection

#### Basic128Rsa15 (Deprecated)
- RSA 1.5 encryption with 128-bit AES
- **Status**: Deprecated due to security vulnerabilities

#### Basic256 (Deprecated)
- RSA OAEP encryption with 256-bit AES
- **Status**: Deprecated

#### Basic256Sha256 (Recommended)
- RSA OAEP encryption with 256-bit AES and SHA-256 hashing
- **Status**: Current standard for secure communications

#### Aes128_Sha256_RsaOaep
- Modern policy with AES-128 encryption
- **Status**: Newer standard

#### Aes256_Sha256_RsaPss
- Most secure policy with AES-256 encryption
- **Status**: Latest and most secure

## Authentication Methods

### 1. Anonymous Authentication
```yaml
authentication:
  enabled: false
  username: null
  password: null
```
- **Description**: No user credentials required
- **Use Case**: Public data or development environments
- **Security**: Minimal - anyone can connect

### 2. Username/Password Authentication
```yaml
authentication:
  enabled: true
  username: "Win11"
  password: "pw"
```
- **Description**: Traditional username/password login
- **Use Case**: Basic user authentication
- **Security**: Moderate - passwords can be compromised

### 3. Certificate-Based Authentication
```yaml
authentication:
  enabled: true
  certificate_file: "client_cert.pem"
  private_key_file: "client_key.pem"
```
- **Description**: Uses X.509 certificates for authentication
- **Use Case**: High-security environments
- **Security**: High - based on cryptographic keys

### 4. Kerberos Authentication
- **Description**: Enterprise authentication using Kerberos tickets
- **Use Case**: Windows domain environments
- **Security**: High - integrated with Active Directory

## Certificate-Based Security

### What Are X.509 Certificates?
X.509 certificates are digital documents that:
- Contain a public key
- Include identity information (Common Name, Organization, etc.)
- Are digitally signed by a Certificate Authority (CA) or self-signed
- Have validity periods (not before/not after dates)

### Certificate Components

#### Public/Private Key Pair
```
Private Key (client_key.pem):
- Kept secret by the client
- Used for decryption and digital signing
- Never shared with anyone

Public Key (embedded in certificate):
- Shared with the server
- Used for encryption and signature verification
- Safe to distribute
```

#### Certificate Information
```
Subject: CN=OPC UA Client Certificate, O=OPC UA Client, C=US
Issuer: CN=OPC UA Client Certificate, O=OPC UA Client, C=US (self-signed)
Valid From: 2024-01-15 10:30:00 UTC
Valid To: 2025-01-15 10:30:00 UTC
Serial Number: 1234567890abcdef
```

### How Certificate Security Works

#### 1. Certificate Exchange
```
Client                    Server
  |                         |
  |--- Certificate -------->|  (Client sends its certificate)
  |<-- Certificate ---------|  (Server sends its certificate)
  |                         |
  |--- Verify Server ------>|  (Client verifies server certificate)
  |<-- Verify Client -------|  (Server verifies client certificate)
```

#### 2. Key Exchange and Encryption
```
1. Client generates random session key
2. Client encrypts session key with server's public key
3. Server decrypts session key with its private key
4. Both parties now have shared session key
5. All further communication encrypted with session key
```

### Certificate Trust Models

#### Self-Signed Certificates
- Certificate signs itself
- No external Certificate Authority
- Must be manually trusted
- Common in industrial environments

#### CA-Signed Certificates
- Signed by trusted Certificate Authority
- Automatically trusted if CA is trusted
- More complex to set up
- Better for enterprise environments

## Security Policies

### Basic256Sha256 (Our Implementation)
```python
SecurityPolicyBasic256Sha256:
  Encryption: RSA-OAEP with SHA-1, AES-256-CBC
  Signing: RSA-PKCS#1 v1.5 with SHA-256
  Key Derivation: P_SHA256
  Certificate: X.509v3 with RSA keys (2048-bit minimum)
```

#### Why Basic256Sha256?
- **Widely Supported**: Compatible with most OPC UA servers
- **Secure**: Uses strong cryptographic algorithms
- **TwinCAT Compatible**: Supported by Beckhoff TwinCAT
- **Future-Proof**: Not deprecated like older policies

## Implementation in Our System

### Backend Architecture

#### 1. Connection Layer (`opcua_discovery/connection.py`)
```python
class OPCUAConnection:
    def __init__(self, server_url, timeout=10, username=None, password=None,
                 cert_file=None, private_key_file=None, security_policy=None):
        # Stores connection parameters
        
    async def connect(self):
        # 1. Create asyncua Client
        # 2. Set up security if configured
        # 3. Set up authentication if configured
        # 4. Connect to server
        
    async def _setup_security(self):
        # Configure certificate-based security
        
    async def _setup_auto_security(self):
        # Generate certificates automatically if needed
```

#### 2. Certificate Generation
```python
def _generate_certificates(self, cert_file, key_file):
    # 1. Generate RSA private key (2048-bit)
    # 2. Create X.509 certificate with:
    #    - Subject information
    #    - Validity period (1 year)
    #    - Key usage extensions
    #    - Subject Alternative Names (localhost, 127.0.0.1)
    # 3. Sign certificate with private key
    # 4. Save both files in PEM format
```

#### 3. Security Configuration Flow
```
1. Check if security is enabled in config
2. If cert files provided: Use existing certificates
3. If security policy set but no certs: Auto-generate certificates
4. Apply security policy to asyncua client
5. Set username/password if provided
6. Attempt connection
```

### Configuration Structure

#### Complete Security Configuration
```yaml
opcua:
  url: "opc.tcp://localhost:4840"
  authentication:
    enabled: true
    username: "Win11"
    password: "pw"
  security:
    enabled: true
    policy: "Basic256Sha256"
    cert_file: "certificates/client_cert.pem"      # Optional
    private_key_file: "certificates/client_key.pem" # Optional
  namespaces: [1, 2]
```

#### Auto-Generated Certificates
```yaml
security:
  enabled: true
  policy: "Basic256Sha256"
  cert_file: null    # Will auto-generate
  private_key_file: null
```

### File Structure
```
project/
├── certificates/           # Auto-generated certificate directory
│   ├── client_cert.pem    # Client certificate (public)
│   └── client_key.pem     # Client private key (secret)
├── config.yaml            # Main configuration
└── opcua_discovery/
    ├── connection.py       # Security implementation
    └── cli.py             # Command-line interface
```

## Practical Usage

### 1. Basic Discovery (No Security)
```bash
# Connect to unsecured OPC UA server
python opcua_discovery/cli.py opc.tcp://localhost:4840
```

### 2. Username/Password Authentication
```bash
# Connect with credentials only
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username Win11 \
  --password pw
```

### 3. Full Security (Recommended for TwinCAT)
```bash
# Connect with authentication + encryption
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username Win11 \
  --password pw \
  --security-policy Basic256Sha256
```

### 4. Using Existing Certificates
```bash
# Use pre-generated certificates
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username Win11 \
  --password pw \
  --cert-file certificates/client_cert.pem \
  --private-key-file certificates/client_key.pem
```

### 5. Generate Certificates Manually
```bash
# Pre-generate certificates
python generate_certificates.py

# Then use them
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --cert-file certificates/client_cert.pem \
  --private-key-file certificates/client_key.pem
```

### Configuration Examples

#### Development Environment (Minimal Security)
```yaml
opcua:
  url: "opc.tcp://localhost:4840"
  authentication:
    enabled: false
  security:
    enabled: false
```

#### Production Environment (Full Security)
```yaml
opcua:
  url: "opc.tcp://production-server:4840"
  authentication:
    enabled: true
    username: "production_user"
    password: "secure_password"
  security:
    enabled: true
    policy: "Basic256Sha256"
    cert_file: "/etc/opcua/certs/client.pem"
    private_key_file: "/etc/opcua/private/client.key"
```

#### TwinCAT Configuration (Your Setup)
```yaml
opcua:
  url: "opc.tcp://localhost:4840"
  authentication:
    enabled: true
    username: "Win11"
    password: "pw"
  security:
    enabled: true
    policy: "Basic256Sha256"
    # cert_file: null  # Auto-generate
    # private_key_file: null
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Certificate Validation Errors
```
Error: Certificate validation failed
```
**Causes:**
- Server doesn't trust client certificate
- Certificate expired
- Certificate hostname mismatch

**Solutions:**
```bash
# Regenerate certificates
python generate_certificates.py

# Check certificate validity
openssl x509 -in certificates/client_cert.pem -text -noout

# For TwinCAT: Import client certificate into trusted store
```

#### 2. Security Policy Mismatch
```
Error: Security policy not supported
```
**Causes:**
- Server doesn't support Basic256Sha256
- Client/server policy mismatch

**Solutions:**
```bash
# Try different security policy
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --security-policy Basic256

# Check server supported policies (if available)
```

#### 3. Authentication Failures
```
Error: Authentication failed
```
**Causes:**
- Wrong username/password
- Account disabled
- Authentication method not supported

**Solutions:**
```bash
# Verify credentials
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username correct_user \
  --password correct_pass

# Try without authentication first
python opcua_discovery/cli.py opc.tcp://localhost:4840
```

#### 4. Connection Timeouts
```
Error: Connection timed out
```
**Causes:**
- Server not running
- Firewall blocking connection
- Wrong port/URL

**Solutions:**
```bash
# Test basic connectivity
telnet localhost 4840

# Try longer timeout
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --timeout 30

# Check server status in TwinCAT
```

### TwinCAT-Specific Issues

#### Certificate Trust Issues
1. **Problem**: TwinCAT rejects client certificate
2. **Solution**:
   - Open TwinCAT OPC UA Configuration
   - Go to Certificate Trust Lists
   - Add client certificate to trusted certificates
   - Restart OPC UA server

#### User Account Issues
1. **Problem**: Username/password not working
2. **Solution**:
   - Check TwinCAT user management
   - Verify user has OPC UA access rights
   - Ensure user account is enabled

## Best Practices

### Security Best Practices

#### 1. Certificate Management
```bash
# Use strong key sizes (2048-bit minimum)
# Rotate certificates regularly (annually)
# Store private keys securely
# Use proper file permissions (600 for private keys)
chmod 600 certificates/client_key.pem
chmod 644 certificates/client_cert.pem
```

#### 2. Authentication
```yaml
# Use strong passwords
# Implement account lockout policies
# Use certificate-based auth for high-security environments
# Rotate credentials regularly
```

#### 3. Network Security
```yaml
# Use VPNs for remote connections
# Implement firewall rules
# Monitor OPC UA traffic
# Use network segmentation
```

### Development vs Production

#### Development Environment
- Use self-signed certificates
- Simple username/password auth
- Relaxed security policies
- Local connections only

#### Production Environment
- CA-signed certificates
- Strong authentication
- Strict security policies
- Network monitoring
- Regular security audits

### Monitoring and Logging

#### Enable Detailed Logging
```bash
# Run with verbose logging
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username Win11 \
  --password pw \
  --security-policy Basic256Sha256 \
  --verbose
```

#### Monitor Security Events
- Failed authentication attempts
- Certificate validation failures
- Unusual connection patterns
- Security policy violations

### Performance Considerations

#### Certificate Operations
- Certificate validation adds overhead
- Cache certificates when possible
- Use appropriate key sizes (2048-bit is good balance)

#### Encryption Impact
- SignAndEncrypt mode has performance cost
- Consider Sign-only for less sensitive data
- Monitor CPU usage on embedded systems

## Summary

OPC UA security provides multiple layers of protection:

1. **Transport Security**: TLS-like encryption at protocol level
2. **Authentication**: Verify user identity
3. **Authorization**: Control access to resources
4. **Message Security**: Sign and encrypt individual messages

Our implementation supports:
- ✅ Username/password authentication
- ✅ Certificate-based encryption (Basic256Sha256)
- ✅ Auto-generated certificates
- ✅ TwinCAT compatibility
- ✅ Flexible configuration

For your TwinCAT setup, the recommended approach is:
```bash
python opcua_discovery/cli.py opc.tcp://localhost:4840 \
  --username Win11 \
  --password pw \
  --security-policy Basic256Sha256
```

This provides both authentication and encryption while maintaining compatibility with TwinCAT's security requirements.
