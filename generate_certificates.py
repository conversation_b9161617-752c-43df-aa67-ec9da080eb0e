#!/usr/bin/env python3
"""
Generate self-signed certificates for OPC UA client connections.

This script creates a client certificate and private key that can be used
for secure OPC UA connections with TwinCAT or other OPC UA servers that
require certificate-based security.
"""

import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone

try:
    from cryptography import x509
    from cryptography.x509.oid import NameOID
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    import ipaddress
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def generate_certificates():
    """Generate self-signed certificates for OPC UA client."""
    try:
        if not CRYPTOGRAPHY_AVAILABLE:
            raise ImportError("cryptography library is required for certificate generation. Install with: pip install cryptography")

        # Create certificates directory
        cert_dir = Path("certificates")
        cert_dir.mkdir(exist_ok=True)

        cert_file = cert_dir / "client_cert.pem"
        key_file = cert_dir / "client_key.pem"

        log.info(f"Generating certificates in: {cert_dir.absolute()}")

        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "OPC UA Client"),
            x509.NameAttribute(NameOID.COMMON_NAME, "OPC UA Client Certificate"),
        ])

        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.now(timezone.utc)
        ).not_valid_after(
            datetime.now(timezone.utc) + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
            ]),
            critical=False,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_agreement=False,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.ExtendedKeyUsage([
                x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH,
                x509.oid.ExtendedKeyUsageOID.SERVER_AUTH,
            ]),
            critical=True,
        ).sign(private_key, hashes.SHA256())

        # Write private key
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        # Write certificate
        with open(cert_file, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        log.info(f"Certificate generated: {cert_file}")
        log.info(f"Private key generated: {key_file}")
        log.info("Certificates generated successfully!")
        
        print("\n" + "="*50)
        print("CERTIFICATE GENERATION COMPLETE")
        print("="*50)
        print(f"Certificate file: {cert_file.absolute()}")
        print(f"Private key file: {key_file.absolute()}")
        print("\nTo use these certificates:")
        print("1. Update your config.yaml with the certificate paths:")
        print("   security:")
        print("     enabled: true")
        print("     policy: Basic256Sha256")
        print(f"     cert_file: {cert_file}")
        print(f"     private_key_file: {key_file}")
        print("\n2. Or use them with the discovery tool:")
        print(f"   python opcua_discovery/cli.py opc.tcp://localhost:4840 \\")
        print(f"     --cert-file {cert_file} --private-key-file {key_file}")
        print("\n3. For TwinCAT servers, you may need to:")
        print("   - Import the client certificate into TwinCAT's trusted certificates")
        print("   - Configure the server to accept the certificate")
        
    except Exception as e:
        log.error(f"Failed to generate certificates: {e}")
        return False
    
    return True


def main():
    """Main function."""
    print("OPC UA Certificate Generator")
    print("="*30)

    try:
        success = generate_certificates()
        if success:
            return 0
        else:
            return 1
    except KeyboardInterrupt:
        log.info("Certificate generation cancelled by user")
        return 1
    except Exception as e:
        log.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
