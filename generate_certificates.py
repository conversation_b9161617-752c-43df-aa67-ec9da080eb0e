#!/usr/bin/env python3
"""
Generate self-signed certificates for OPC UA client connections.

This script creates a client certificate and private key that can be used
for secure OPC UA connections with TwinCAT or other OPC UA servers that
require certificate-based security.
"""

import asyncio
import logging
from pathlib import Path
from asyncua import Client

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


async def generate_certificates():
    """Generate self-signed certificates for OPC UA client."""
    try:
        # Create certificates directory
        cert_dir = Path("certificates")
        cert_dir.mkdir(exist_ok=True)
        
        cert_file = cert_dir / "client_cert.pem"
        key_file = cert_dir / "client_key.pem"
        
        log.info(f"Generating certificates in: {cert_dir.absolute()}")
        
        # Create a temporary client to generate certificates
        client = Client("opc.tcp://dummy:4840")  # Dummy URL, we won't connect
        
        # Generate the certificates
        await client.create_keys(str(cert_file), str(key_file))
        
        log.info(f"Certificate generated: {cert_file}")
        log.info(f"Private key generated: {key_file}")
        log.info("Certificates generated successfully!")
        
        print("\n" + "="*50)
        print("CERTIFICATE GENERATION COMPLETE")
        print("="*50)
        print(f"Certificate file: {cert_file.absolute()}")
        print(f"Private key file: {key_file.absolute()}")
        print("\nTo use these certificates:")
        print("1. Update your config.yaml with the certificate paths:")
        print("   security:")
        print("     enabled: true")
        print("     policy: Basic256Sha256")
        print(f"     cert_file: {cert_file}")
        print(f"     private_key_file: {key_file}")
        print("\n2. Or use them with the discovery tool:")
        print(f"   python opcua_discovery/cli.py opc.tcp://localhost:4840 \\")
        print(f"     --cert-file {cert_file} --private-key-file {key_file}")
        print("\n3. For TwinCAT servers, you may need to:")
        print("   - Import the client certificate into TwinCAT's trusted certificates")
        print("   - Configure the server to accept the certificate")
        
    except Exception as e:
        log.error(f"Failed to generate certificates: {e}")
        return False
    
    return True


def main():
    """Main function."""
    print("OPC UA Certificate Generator")
    print("="*30)
    
    try:
        success = asyncio.run(generate_certificates())
        if success:
            return 0
        else:
            return 1
    except KeyboardInterrupt:
        log.info("Certificate generation cancelled by user")
        return 1
    except Exception as e:
        log.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
