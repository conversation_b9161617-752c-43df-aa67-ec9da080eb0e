import logging
from influxdb_client import InfluxDBC<PERSON>, Point
from influxdb_client.client.write_api import ASYNCHRONOUS

log = logging.getLogger("manufacturing_monitor.influx_writer")

class InfluxWriter:
    """
    Handles communication and writes to the InfluxDB server.
    """

    def __init__(self, influx_config):
        """
        Initializes the InfluxWriter and validates the connection to the InfluxDB server.

        Args:
            influx_config (dict): Sub-dictionary from config.yaml with InfluxDB details.
        """
        self.url = influx_config.get("url")
        self.token = influx_config.get("token")
        self.org = influx_config.get("org")
        self.bucket = influx_config.get("bucket")

        log.info(f"Initializing InfluxWriter for URL: {self.url} and Org: {self.org}")

        # Validate parameters from config.yaml
        if not all([self.url, self.token, self.org, self.bucket]):
            log.critical("InfluxDB configuration is incomplete. Check url, token, org, and bucket.")
            raise ValueError("Incomplete InfluxDB configuration.")

        try:
            self.client = InfluxDBClient(url=self.url, token=self.token, org=self.org)
            # Validate connection and authentication
            if not self.client.ping():
                raise ConnectionError("Ping to InfluxDB failed. Check URL and token.")

            self.writer = self.client.write_api(write_options=ASYNCHRONOUS)
            
            log.info("InfluxWriter initialized and connection confirmed.")

        except Exception as e:
            log.critical(f"Failed to initialize InfluxWriter: {e}")
            # Re-raise the exception to ensure the main application knows it failed and stops.
            raise

    def write(self, measurement, fields, tags=None, timestamp=None):
        """
        Formats and queues a single data point to be written to InfluxDB

        This method is non-blocking.
        This method adds the data point to an internal buffer and returns immediately.

        Args:
            measurement (str): Name of the table where the data point will go.
            fields (dict): Dictionary of measured values.
            tags (dict): Metadata to organise the data with. Optional as data may be simple.
            timestamp (datetime): Time of receiving data. Optional as InfluxDB server will auto-assign current time upon receipt if not provided.
        """
        try:
            point = Point(measurement)

            if tags:
                for key, value in tags.items():
                    point.tag(key, value)

            for key, value in fields.items():
                point.field(key, value)

            if timestamp:
                point.time(timestamp)

            self.writer.write(bucket=self.bucket, record=point)
            log.debug(f"Queued point for for InfluxDB: {point.to_line_protocol}")
        
        except Exception as e:
            log.error(f"Failed to create and queue InfluxDB point: {e}")

    def close(self):
        """
        Flushes the buffer when application shuts down to prevent data loss.
        This method is defensive and will swallow errors raised while closing
        underlying resources to avoid exceptions during interpreter teardown.
        """
        try:
            if getattr(self, "writer", None) is not None:
                try:
                    self.writer.close()
                except Exception as e:
                    log.debug(f"Influx write_api.close() raised: {e}")
                finally:
                    self.writer = None

            if getattr(self, "client", None) is not None:
                try:
                    self.client.close()
                except Exception as e:
                    log.debug(f"Influx client.close() raised: {e}")
                finally:
                    self.client = None

            log.info("InfluxWriter successfully flushed and closed!")
        except Exception as e:
            # Catch-all: during interpreter shutdown handles/queues may be invalid (WinError 6)
            log.debug(f"Exception during InfluxWriter.close(): {e}")
            