# main.py
import asyncio
import logging
import sys
import os

from startup import initialize_logging, load_and_validate_config
from influx_writer import InfluxWriter
from monitors.opcua_monitor import monitor_opcua_machine

# Global logger will be configured after setup_logger is called
log = None


async def run_idle_service(idle_message: str = "Service running with no machines to monitor..."):
    """
    Run an idle loop that logs periodically until interrupted by the user.

    This centralizes the "keep service running" logic used in a few places
    so the main function remains at a single level of abstraction.
    """
    try:
        while True:
            await asyncio.sleep(60)
            log.info(idle_message)
    except KeyboardInterrupt:
        log.info("Service stopped by user")

async def main():
    """The main entry point for the manufacturing monitoring service."""
    
    # Step 1: Initialize logging system and assign the global logger here
    logger, _ = initialize_logging()
    if not logger:
        sys.exit(1)
    global log
    log = logger.getChild("manufacturing_monitor.main")
    
    influx_writer = None
    try:
        # Step 2: Load and validate configuration
        config = load_and_validate_config()
        if not config:
            log.critical("Failed to load configuration. Exiting.")
            sys.exit(1)
        
        # Step 3: Determine environment
        environment = os.getenv('ENVIRONMENT', 'development').lower()
        env_config = config[environment]
        log.info(f"Running in {environment} mode")
        
        # Step 4: Initialize InfluxDB writer
        log.info("Initializing InfluxDB connection...")
        try:
            influx_writer = InfluxWriter(env_config['influxdb'])
            log.info("InfluxDB writer initialized successfully")
        except Exception as e:
            log.critical(f"Failed to initialize InfluxDB writer: {e}")
            log.critical("Cannot continue without database connection")
            sys.exit(1)
        
        # Step 5: Start monitoring machines
        machines = env_config['machines']
        if not machines:
            log.warning("No machines configured for monitoring")
            log.info("Service will run but no data will be collected")
            
            # Keep the service running until user shuts down
            await run_idle_service("Service running with no machines to monitor...")
            return
        
        log.info(f"Starting monitoring for {len(machines)} machines...")
        
        # Create monitoring tasks for each machine
        monitoring_tasks = []
        for machine_name, machine_config in machines.items():
            log.info(f"Creating monitoring task for machine: {machine_name}")
            
            # Determine protocol type
            protocol = machine_config.get('protocol', 'opcua')
            
            if protocol == 'opcua':
                task = asyncio.create_task(
                    monitor_opcua_machine(machine_name, machine_config, influx_writer),
                    name=f"monitor_opcua_{machine_name}"
                )
            else:
                log.error(f"Unsupported protocol '{protocol}' for machine '{machine_name}'. Skipping.")
                continue
                
            monitoring_tasks.append(task)
            log.info(f"Created {protocol.upper()} monitoring task for machine: {machine_name}")
        
        if not monitoring_tasks:
            log.warning("No valid monitoring tasks were created")
            log.info("Service will run but no data will be collected")

            # Keep the service running
            await run_idle_service("Service running with no valid machines to monitor...")
            return
        
        log.info("All monitoring tasks created successfully")
        log.info("Manufacturing Monitor is now running...")
        log.info("Press Ctrl+C to stop the service")
        
        # Wait for all monitoring tasks to complete (they should run indefinitely) 
        # Exits infinite loop in monitoring tasks if 'Ctrl+C' is triggered for graceful shutdown
        try:
            await asyncio.gather(*monitoring_tasks)
        except KeyboardInterrupt:
            log.info("Shutdown signal received. Stopping monitoring tasks...")
            
            for task in monitoring_tasks:
                if not task.done():
                    task.cancel()
            
            await asyncio.gather(*monitoring_tasks, return_exceptions=True)
            log.info("All monitoring tasks stopped")
        
    except Exception as e:
        log.critical(f"Unexpected error in main application: {e}")
        log.critical("Application will exit")
        sys.exit(1)
    
    finally:
        try:
            if influx_writer is not None:
                log.info("Closing InfluxDB writer...")
                influx_writer.close()
                log.info("InfluxDB writer closed.")
        except Exception:
            log.exception("Error while closing InfluxDB writer during shutdown")
        
        log.info("Manufacturing Monitor shutdown complete")
        log.info("="*60)

if __name__ == "__main__":
    try:
        # Run the async main function
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error starting application: {e}")
        sys.exit(1)