import asyncio
import logging
from asyncua import C<PERSON>, Node
from asyncua.common.subscription import DataChangeNotif

log = logging.getLogger("manufacturing_monitor.opcua")

async def monitor_opcua_machine(machine_name, machine_config, influx_writer):
    """
    Continuously monitors a single OPC UA machine.

    Connects to the server, subscribes to specified nodes, and writes
    data changes to InfluxDB. It will automatically attempt to reconnect
    if the connection is lost.

    Args:
        machine_name (str): The name of the machine being monitored.
        machine_config (dict): Configuration dictionary for the machine, including OPC UA connection details and node subscriptions.
        influx_writer (InfluxWriter): Instance responsible for writing data points to InfluxDB.
    """
    opcua_config = machine_config.get("opcua", {})
    url = opcua_config.get("url")

    if not url:
        log.error(f"OPC UA URL is missing for machine '{machine_name}'. Skipping this monitor.")
        return

    log.info(f"Starting OPC UA monitor for '{machine_name}' at {url}")
    
    monitoring_config = machine_config.get("monitoring", {})
    timeout_duration = monitoring_config.get("connection_timeout_seconds", 5)
    subscription_interval = monitoring_config.get("subscription_interval_ms", 500)
    retry_duration = monitoring_config.get("retry_delay_seconds", 5)
    while True:
        try:
            async with Client(url=url, timeout=timeout_duration) as machine:
                log.info(f"Successfully connected to OPC UA server: {url}")
                name_to_id = machine_config["nodes"]
                id_to_name = {v: k for k, v in name_to_id.items()}
                static_tags = {
                    "type": machine_config["type"],
                    "name": machine_name
                    }
                handler = OpcuaDataHandler(influx_writer, machine_name, id_to_name, static_tags)
                subscription = await machine.create_subscription(period=subscription_interval, handler=handler)

                # Build list of node objects to subscribe to and log them for debugging
                nodes_to_monitor = []
                for node_id in id_to_name.keys():
                    try:
                        nodes_to_monitor.append(machine.get_node(node_id))
                    except Exception as e:
                        log.warning(f"Could not get node object for {node_id}: {e}")

                log.info(f"Subscribing to {len(nodes_to_monitor)} nodes for machine '{machine_name}'")
                if nodes_to_monitor:
                    await subscription.subscribe_data_change(nodes_to_monitor)

                # Hold the successful connection open
                while True:
                    await asyncio.sleep(1)
        except TimeoutError as e:
            log.error(f"Connection timed out after {timeout_duration}s")
        except Exception as e:
            log.error(f"Error monitoring OPC UA server at {url}: {e}")
            log.info("Attempting to reconnect in 10 seconds...")
            await asyncio.sleep(retry_duration)

class OpcuaDataHandler:
    """Handles incoming data changes from an OPC UA subscription."""
    def __init__(self, influx_writer, machine_name, id_to_name, static_tags=None):
        """
        Initializes the data handler.

        Args:
            influx_writer: An instance of our InfluxWriter class.
            machine_name (str): The name of the machine for tagging.
            static_tags (dict): A dictionary of constant metadata tags for the machine being monitored. These tags are applied to every data point received by this handler and are used for filtering and grouping in InfluxDB queries.
            node_map (dict): A mapping from OPC UA node ID (str) to
                             clean InfluxDB field name (str).
        """
        self.influx_writer = influx_writer
        self.machine_name = machine_name
        self.id_to_name = id_to_name
        self.static_tags = static_tags
        log.info(f"OpcuaDataHandler initialized for machine '{machine_name}'.")

    def datachange_notification(self, node: Node, val, data: DataChangeNotif):
        """
        This method is called automatically by the asyncua library when a
        subscribed node's value changes.
        """
        node_id = str(node)
        field_name = self.id_to_name.get(node_id)
        if not field_name:
            log.warning(f"Received data for an unmapped node: {node_id}. Skipping.")
            return
        timestamp = data.monitored_item.Value.SourceTimestamp
        tags = self.static_tags
        fields = {field_name: val}
        self.influx_writer.write("machine_sensors", fields, tags, timestamp)

        log.debug(f"Received data change for {self.machine_name}: {node_id} = {val}")