# OPC UA Discovery Package

A modular Python package for discovering OPC UA nodes and generating machine configuration files.

## Overview

This package provides a clean, modular approach to OPC UA node discovery with proper separation of concerns. It has been refactored from a single large file into focused modules that are easier to maintain, test, and extend.

## Architecture

The package consists of the following modules:

### Core Components

- **`connection.py`** - Handles OPC UA server connections and disconnections
- **`node_browser.py`** - Recursively browses and discovers OPC UA nodes
- **`field_normalizer.py`** - Normalizes node names into clean field names
- **`config_generator.py`** - Generates machine configuration files
- **`discoverer.py`** - Main orchestrator class that ties everything together

### CLI Interface

- **`cli.py`** - Command-line interface (used as a module)
- **`../discover_nodes_cli.py`** - Standalone CLI script

## Usage

### As a Python Package

```python
import asyncio
from opcua_discovery import OPCUANodeDiscoverer

async def main():
    discoverer = OPCUANodeDiscoverer("opc.tcp://localhost:4840")
    
    # Discover nodes
    nodes = await discoverer.discover_nodes()
    
    # Generate configuration
    config = discoverer.generate_machine_config(
        nodes, 
        machine_name="EOS_Printer_A", 
        machine_type="3d_printer"
    )
    
    # Save to file
    discoverer.save_config_to_file(config)

asyncio.run(main())
```

### Command Line Interface

```bash
# Basic discovery
python discover_nodes_cli.py opc.tcp://localhost:4840

# With custom machine name and type
python discover_nodes_cli.py opc.tcp://localhost:4840 --machine-name "EOS_Printer_A" --machine-type "3d_printer"

# With custom timeout and depth
python discover_nodes_cli.py opc.tcp://localhost:4840 --timeout 30 --max-depth 10

# Verbose logging
python discover_nodes_cli.py opc.tcp://localhost:4840 --verbose
```

## Features

- **Modular Design**: Each component has a single responsibility
- **Error Handling**: Robust error handling for network and OPC UA issues
- **Field Name Normalization**: Converts complex node paths to clean field names
- **Collision Resolution**: Handles duplicate field names automatically
- **Configuration Generation**: Creates ready-to-use YAML configuration files
- **Namespace Filtering**: Focuses on custom namespaces, ignoring system nodes
- **Configurable Depth**: Prevents infinite recursion with configurable max depth
- **Logging**: Comprehensive logging at different levels

## Class Relationships

```
OPCUANodeDiscoverer
├── OPCUAConnection (manages server connection)
├── NodeBrowser (browses node tree)
└── ConfigGenerator (generates config files)
    └── FieldNameNormalizer (normalizes field names)
```

## Configuration Output

The package generates YAML configuration files in this format:

```yaml
development:
  machines:
    Machine_localhost_4840:
      type: "3d_printer"
      opcua:
        url: "opc.tcp://localhost:4840"
        namespaces: [2, 3, 4]
        authentication:
          enabled: false
          username: null
          password: null
      nodes:
        temperature: "ns=2;i=1001"
        pressure: "ns=2;i=1002"
        status: "ns=3;s=MachineStatus"
      monitoring:
        subscription_interval_ms: 1000
        connection_timeout_seconds: 10
        retry_attempts: 3
        retry_delay_seconds: 5
```

## Benefits of This Architecture

1. **Maintainability**: Each module has a clear, focused responsibility
2. **Testability**: Components can be unit tested independently
3. **Reusability**: Individual modules can be used in other projects
4. **Extensibility**: Easy to add new features without modifying existing code
5. **Readability**: Smaller, focused files are easier to understand
6. **Debugging**: Issues can be isolated to specific modules

## Dependencies

- `asyncua` - OPC UA client library
- `pyyaml` - YAML file handling
- Standard library modules: `logging`, `re`, `os`, `datetime`, `urllib.parse`

## Error Handling

The package handles various error conditions:

- Connection timeouts
- Server unavailability
- Authentication failures
- Node read errors
- File I/O errors
- Malformed URLs
- Deep recursion protection
