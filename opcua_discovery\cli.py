"""Command-line interface for OPC UA node discovery."""
import argparse
import asyncio
import logging
import sys
import os
from pathlib import Path

# Handle both direct execution and module import
try:
    # Try relative import (when run as module)
    from .discoverer import OPCUANodeDiscoverer
except ImportError:
    # Fall back to absolute import (when run directly)
    # Add the parent directory to the path so we can import the opcua_discovery package
    parent_dir = Path(__file__).resolve().parent.parent
    sys.path.insert(0, str(parent_dir))
    from opcua_discovery.discoverer import OPCUANodeDiscoverer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


async def discover_and_configure(server_url, machine_name=None, machine_type="unknown", timeout=10, max_depth=6, username=None, password=None):
    """
    Main function to discover nodes and generate configuration.

    Args:
        server_url (str): OPC UA server URL
        machine_name (str): Optional machine name
        machine_type (str): Type of machine
        timeout (int): Connection timeout
        max_depth (int): Maximum recursion depth
        username (str): Username for authentication (optional)
        password (str): Password for authentication (optional)
    """
    discoverer = OPCUANodeDiscoverer(server_url, timeout, max_depth, username, password)
    
    try:
        # Discover nodes
        nodes = await discoverer.discover_nodes()
        
        if not nodes:
            log.error("No nodes discovered. Check server connection and configuration.")
            return
        
        # Print summary and generate config
        _print_discovery_summary(server_url, nodes)
        config = discoverer.generate_machine_config(nodes, machine_name, machine_type)
        discoverer.save_config_to_file(config)
        
        print(f"\n=== CONFIGURATION GENERATED ===")
        print("Machine configuration has been added to the config file.")
        print("Review and modify the generated configuration as needed.")
        
    except KeyboardInterrupt:
        log.info("Discovery interrupted by user")
    except Exception as e:
        log.error(f"Error during discovery and configuration: {e}")


def _print_discovery_summary(server_url, nodes):
    """
    Print a summary of discovered nodes.
    
    Args:
        server_url (str): OPC UA server URL
        nodes (dict): Dictionary of discovered nodes
    """
    print(f"\n=== DISCOVERY SUMMARY ===")
    print(f"Server: {server_url}")
    print(f"Total sensor nodes discovered: {len(nodes)}")
    
    if nodes:
        # Show namespace distribution
        namespaces = {}
        for node_info in nodes.values():
            ns = node_info.get('namespace', 'unknown')
            namespaces[ns] = namespaces.get(ns, 0) + 1
        
        print(f"Namespace distribution (ns: no. of nodes): {dict(namespaces)}")
        
        print("\nDiscovered sensor nodes:")
        for i, (path, node_info) in enumerate(nodes.items()):
            if i >= 20:  # Show first 20 nodes
                print(f"... and {len(nodes) - 20} more nodes")
                break
            ns = node_info.get('namespace', '?')
            print(f"  [{ns}] {path}: {node_info['node_id']} = {node_info['value']}")


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Discover OPC UA nodes on a server and generate configuration files.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
            Examples:
            python cli.py opc.tcp://localhost:4840
            python cli.py opc.tcp://localhost:4840 --machine-name "EOS_Printer_A" --machine-type "3d_printer"
            python cli.py opc.tcp://localhost:4840 --timeout 30 --max-depth 10
            python cli.py opc.tcp://localhost:4840 --username Win11 --password pw
        """
    )

    # Required arguments
    parser.add_argument("url", type=str, help="OPC UA server URL (e.g., opc.tcp://localhost:4840)")
    
    # Optional arguments
    parser.add_argument("--machine-name", type=str, help="Name for the machine in config")
    parser.add_argument("--machine-type", type=str, default="unknown",
                       help="Type of machine (e.g., '3d_printer', 'cnc_machine', 'power_meter')")
    parser.add_argument("--timeout", type=int, default=10,
                       help="Connection timeout in seconds (default: 10)")
    parser.add_argument("--max-depth", type=int, default=5,
                       help="Maximum recursion depth for node discovery (default: 6)")
    parser.add_argument("--username", type=str,
                       help="Username for OPC UA authentication")
    parser.add_argument("--password", type=str,
                       help="Password for OPC UA authentication")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate URL format
    if not args.url.startswith("opc.tcp://"):
        log.error("URL must start with 'opc.tcp://'")
        return 1
    
    print(f"=== OPC UA NODE DISCOVERER ===")
    print(f"Server URL: {args.url}")
    print(f"Machine Type: {args.machine_type}")
    print(f"Timeout: {args.timeout}s")
    print(f"Max Depth: {args.max_depth}")
    print("=" * 40)
    
    try:
        asyncio.run(discover_and_configure(
            args.url, args.machine_name, args.machine_type, args.timeout, args.max_depth, args.username, args.password
        ))
    except Exception as e:
        log.error(f"Fatal error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
