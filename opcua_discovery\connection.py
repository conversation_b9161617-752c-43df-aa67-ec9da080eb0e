"""OPC UA Connection Management"""
import logging
import os
from pathlib import Path
from datetime import datetime, timedelta, timezone
from asyncua import Client
from asyncua.ua import UaStatusCodeError
from asyncua.crypto.security_policies import SecurityPolicyBasic256Sha256

try:
    from cryptography import x509
    from cryptography.x509.oid import NameO<PERSON>
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

log = logging.getLogger(__name__)


class OPCUAConnection:
    """Handles OPC UA server connection and disconnection."""

    def __init__(self, server_url, timeout=10, username=None, password=None,
                 cert_file=None, private_key_file=None, security_policy=None):
        """
        Initialize the OPC UA connection.

        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
            username (str): Username for authentication (optional)
            password (str): Password for authentication (optional)
            cert_file (str): Path to client certificate file (optional)
            private_key_file (str): Path to client private key file (optional)
            security_policy (str): Security policy to use (optional)
        """
        self.server_url = server_url
        self.timeout = timeout
        self.username = username
        self.password = password
        self.cert_file = cert_file
        self.private_key_file = private_key_file
        self.security_policy = security_policy
        self.client = None
    
    async def connect(self):
        """
        Connect to the OPC UA server with proper error handling.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            log.info(f"Attempting to connect to OPC UA server: {self.server_url}")
            self.client = Client(url=self.server_url, timeout=self.timeout)

            # Set up certificates and security if provided
            if self.cert_file and self.private_key_file:
                log.info("Setting up certificate-based security")
                await self._setup_security()
            elif self.security_policy:
                log.info("Setting up security policy without certificates (auto-generated)")
                await self._setup_auto_security()

            # Set authentication if credentials are provided
            if self.username and self.password:
                log.info(f"Using username/password authentication for user: {self.username}")
                self.client.set_user(self.username)
                self.client.set_password(self.password)

            await self.client.connect()
            log.info("Successfully connected to OPC UA server")
            return True
            
        except TimeoutError:
            log.error(f"Connection timed out after {self.timeout} seconds")
            return False
        except UaStatusCodeError as e:
            log.error(f"OPC UA status error: {e}")
            return False
        except ConnectionRefusedError:
            log.error(f"Connection refused. Make sure the server is running at {self.server_url}")
            return False
        except Exception as e:
            log.error(f"Unexpected error connecting to server: {e}")
            return False
    
    async def disconnect(self):
        """Safely disconnect from the OPC UA server."""
        try:
            if self.client:
                await self.client.disconnect()
                log.info("Disconnected from OPC UA server")
        except Exception as e:
            log.warning(f"Error during disconnection: {e}")
    
    async def _setup_security(self):
        """Set up certificate-based security."""
        try:
            await self.client.set_security(
                SecurityPolicyBasic256Sha256,
                certificate=self.cert_file,
                private_key=self.private_key_file,
                server_certificate=None  # Will be retrieved automatically
            )
            log.info("Certificate-based security configured")
        except Exception as e:
            log.error(f"Failed to set up certificate security: {e}")
            raise

    async def _setup_auto_security(self):
        """Set up security with auto-generated certificates."""
        try:
            # Create certificates directory if it doesn't exist
            cert_dir = Path("certificates")
            cert_dir.mkdir(exist_ok=True)

            cert_file = cert_dir / "client_cert.pem"
            key_file = cert_dir / "client_key.pem"

            # Generate certificates if they don't exist
            if not cert_file.exists() or not key_file.exists():
                log.info("Generating self-signed certificates for OPC UA client")
                self._generate_certificates(str(cert_file), str(key_file))

            await self.client.set_security(
                SecurityPolicyBasic256Sha256,
                certificate=str(cert_file),
                private_key=str(key_file),
                server_certificate=None  # Will be retrieved automatically
            )
            log.info("Auto-generated certificate security configured")
        except Exception as e:
            log.error(f"Failed to set up auto-generated certificate security: {e}")
            raise

    def _generate_certificates(self, cert_file, key_file):
        """Generate self-signed certificate and private key for OPC UA client."""
        if not CRYPTOGRAPHY_AVAILABLE:
            raise ImportError("cryptography library is required for certificate generation. Install with: pip install cryptography")

        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "OPC UA Client"),
            x509.NameAttribute(NameOID.COMMON_NAME, "OPC UA Client Certificate"),
        ])

        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.now(timezone.utc)
        ).not_valid_after(
            datetime.now(timezone.utc) + timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.IPAddress("127.0.0.1".encode()),
            ]),
            critical=False,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_agreement=False,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.ExtendedKeyUsage([
                x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH,
                x509.oid.ExtendedKeyUsageOID.SERVER_AUTH,
            ]),
            critical=True,
        ).sign(private_key, hashes.SHA256())

        # Write private key
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        # Write certificate
        with open(cert_file, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))

        log.info(f"Generated certificate: {cert_file}")
        log.info(f"Generated private key: {key_file}")

    def get_node(self, node_id):
        """
        Get a node from the connected client.

        Args:
            node_id (str): Node ID to retrieve

        Returns:
            Node: OPC UA node object

        Raises:
            RuntimeError: If not connected to OPC UA server
        """
        if not self.client:
            raise RuntimeError("Not connected to OPC UA server")
        return self.client.get_node(node_id)
