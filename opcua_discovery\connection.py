"""OPC UA Connection Management"""
import logging
import os
from pathlib import Path
from asyncua import Client
from asyncua.ua import UaStatusCodeError
from asyncua.crypto.security_policies import SecurityPolicyBasic256Sha256

log = logging.getLogger(__name__)


class OPCUAConnection:
    """Handles OPC UA server connection and disconnection."""

    def __init__(self, server_url, timeout=10, username=None, password=None,
                 cert_file=None, private_key_file=None, security_policy=None):
        """
        Initialize the OPC UA connection.

        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
            username (str): Username for authentication (optional)
            password (str): Password for authentication (optional)
            cert_file (str): Path to client certificate file (optional)
            private_key_file (str): Path to client private key file (optional)
            security_policy (str): Security policy to use (optional)
        """
        self.server_url = server_url
        self.timeout = timeout
        self.username = username
        self.password = password
        self.cert_file = cert_file
        self.private_key_file = private_key_file
        self.security_policy = security_policy
        self.client = None
    
    async def connect(self):
        """
        Connect to the OPC UA server with proper error handling.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            log.info(f"Attempting to connect to OPC UA server: {self.server_url}")
            self.client = Client(url=self.server_url, timeout=self.timeout)

            # Set up certificates and security if provided
            if self.cert_file and self.private_key_file:
                log.info("Setting up certificate-based security")
                await self._setup_security()
            elif self.security_policy:
                log.info("Setting up security policy without certificates (auto-generated)")
                await self._setup_auto_security()

            # Set authentication if credentials are provided
            if self.username and self.password:
                log.info(f"Using username/password authentication for user: {self.username}")
                self.client.set_user(self.username)
                self.client.set_password(self.password)

            await self.client.connect()
            log.info("Successfully connected to OPC UA server")
            return True
            
        except TimeoutError:
            log.error(f"Connection timed out after {self.timeout} seconds")
            return False
        except UaStatusCodeError as e:
            log.error(f"OPC UA status error: {e}")
            return False
        except ConnectionRefusedError:
            log.error(f"Connection refused. Make sure the server is running at {self.server_url}")
            return False
        except Exception as e:
            log.error(f"Unexpected error connecting to server: {e}")
            return False
    
    async def disconnect(self):
        """Safely disconnect from the OPC UA server."""
        try:
            if self.client:
                await self.client.disconnect()
                log.info("Disconnected from OPC UA server")
        except Exception as e:
            log.warning(f"Error during disconnection: {e}")
    
    async def _setup_security(self):
        """Set up certificate-based security."""
        try:
            await self.client.set_security(
                SecurityPolicyBasic256Sha256,
                certificate=self.cert_file,
                private_key=self.private_key_file,
                server_certificate=None  # Will be retrieved automatically
            )
            log.info("Certificate-based security configured")
        except Exception as e:
            log.error(f"Failed to set up certificate security: {e}")
            raise

    async def _setup_auto_security(self):
        """Set up security with auto-generated certificates."""
        try:
            # Create certificates directory if it doesn't exist
            cert_dir = Path("certificates")
            cert_dir.mkdir(exist_ok=True)

            cert_file = cert_dir / "client_cert.pem"
            key_file = cert_dir / "client_key.pem"

            # Generate certificates if they don't exist
            if not cert_file.exists() or not key_file.exists():
                log.info("Generating self-signed certificates for OPC UA client")
                await self.client.create_keys(str(cert_file), str(key_file))

            await self.client.set_security(
                SecurityPolicyBasic256Sha256,
                certificate=str(cert_file),
                private_key=str(key_file),
                server_certificate=None  # Will be retrieved automatically
            )
            log.info("Auto-generated certificate security configured")
        except Exception as e:
            log.error(f"Failed to set up auto-generated certificate security: {e}")
            raise

    def get_node(self, node_id):
        """
        Get a node from the connected client.

        Args:
            node_id (str): Node ID to retrieve

        Returns:
            Node: OPC UA node object

        Raises:
            RuntimeError: If not connected to OPC UA server
        """
        if not self.client:
            raise RuntimeError("Not connected to OPC UA server")
        return self.client.get_node(node_id)
