"""Main OPC UA Node Discoverer"""
import logging
from .connection import OPCUAConnection
from .node_browser import <PERSON>de<PERSON><PERSON><PERSON>
from .config_generator import ConfigGenerator

log = logging.getLogger(__name__)


class OPCUANodeDiscoverer:
    """
    Main class that orchestrates OPC UA node discovery.
    
    This class provides a simplified interface for discovering OPC UA nodes
    and generating configuration files.
    """
    
    def __init__(self, server_url, timeout=10, max_depth=5, username=None, password=None,
                 cert_file=None, private_key_file=None, security_policy=None):
        """
        Initialize the node discoverer.

        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
            max_depth (int): Maximum recursion depth for node discovery
            username (str): Username for authentication (optional)
            password (str): Password for authentication (optional)
            cert_file (str): Path to client certificate file (optional)
            private_key_file (str): Path to client private key file (optional)
            security_policy (str): Security policy to use (optional)
        """
        self.server_url = server_url
        self.timeout = timeout
        self.max_depth = max_depth
        self.username = username
        self.password = password
        self.cert_file = cert_file
        self.private_key_file = private_key_file
        self.security_policy = security_policy
        self.connection = OPCUAConnection(server_url, timeout, username, password,
                                        cert_file, private_key_file, security_policy)
        self.browser = NodeBrowser(self.connection, max_depth)
        self.config_generator = ConfigGenerator(server_url)
    
    async def discover_nodes(self):
        """
        Discover all nodes on the server.
        
        Returns:
            dict: Dictionary of discovered nodes
        """
        if not await self.connection.connect():
            log.error("Failed to connect to OPC UA server")
            return {}
        
        try:
            return await self.browser.browse_all_nodes()
        except Exception as e:
            log.error(f"Error during node discovery: {e}")
            return {}
        finally:
            await self.connection.disconnect()
    
    def generate_machine_config(self, discovered_nodes, machine_name=None, machine_type="unknown"):
        """
        Generate machine configuration from discovered nodes.
        
        Args:
            discovered_nodes (dict): Dictionary of discovered nodes
            machine_name (str): Name for the machine (optional)
            machine_type (str): Type of machine
            
        Returns:
            dict: Machine configuration dictionary
        """
        return self.config_generator.generate_machine_config(
            discovered_nodes, machine_name, machine_type
        )
    
    def save_config_to_file(self, config, filename=None):
        """
        Save configuration to file.
        
        Args:
            config (dict): Configuration dictionary
            filename (str): Output filename (optional)
        """
        self.config_generator.save_config_to_file(config, filename)
