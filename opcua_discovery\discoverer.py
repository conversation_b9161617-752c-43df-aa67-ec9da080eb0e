"""Main OPC UA Node Discoverer"""
import logging
from .connection import OPCUAConnection
from .node_browser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .config_generator import ConfigGenerator

log = logging.getLogger(__name__)


class OPCUANodeDiscoverer:
    """
    Main class that orchestrates OPC UA node discovery.
    
    This class provides a simplified interface for discovering OPC UA nodes
    and generating configuration files.
    """
    
    def __init__(self, server_url, timeout=10, max_depth=5, username=None, password=None):
        """
        Initialize the node discoverer.

        Args:
            server_url (str): OPC UA server URL
            timeout (int): Connection timeout in seconds
            max_depth (int): Maximum recursion depth for node discovery
            username (str): Username for authentication (optional)
            password (str): Password for authentication (optional)
        """
        self.server_url = server_url
        self.timeout = timeout
        self.max_depth = max_depth
        self.username = username
        self.password = password
        self.connection = OPCUAConnection(server_url, timeout, username, password)
        self.browser = NodeBrowser(self.connection, max_depth)
        self.config_generator = ConfigGenerator(server_url)
    
    async def discover_nodes(self):
        """
        Discover all nodes on the server.
        
        Returns:
            dict: Dictionary of discovered nodes
        """
        if not await self.connection.connect():
            log.error("Failed to connect to OPC UA server")
            return {}
        
        try:
            return await self.browser.browse_all_nodes()
        except Exception as e:
            log.error(f"Error during node discovery: {e}")
            return {}
        finally:
            await self.connection.disconnect()
    
    def generate_machine_config(self, discovered_nodes, machine_name=None, machine_type="unknown"):
        """
        Generate machine configuration from discovered nodes.
        
        Args:
            discovered_nodes (dict): Dictionary of discovered nodes
            machine_name (str): Name for the machine (optional)
            machine_type (str): Type of machine
            
        Returns:
            dict: Machine configuration dictionary
        """
        return self.config_generator.generate_machine_config(
            discovered_nodes, machine_name, machine_type
        )
    
    def save_config_to_file(self, config, filename=None):
        """
        Save configuration to file.
        
        Args:
            config (dict): Configuration dictionary
            filename (str): Output filename (optional)
        """
        self.config_generator.save_config_to_file(config, filename)
