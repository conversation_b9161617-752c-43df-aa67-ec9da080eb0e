"""Field Name Normalization"""
import re
import logging

log = logging.getLogger(__name__)


class FieldNameNormalizer:
    """Handles field name normalization and collision resolution."""
    
    @staticmethod
    def normalize_field_name(path):
        """
        Convert a node path into a clean, normalized field name.
        
        The normalization process:
        1. Extract the last segment of the path (e.g., "Temperature Value" from "Device/Subsystem/Temperature Value")
        2. Replace non-alphanumeric characters with underscores
        3. Convert to lowercase
        4. Remove common prefixes/suffixes to clean up the name
        5. Handle edge cases (empty names)
        
        Args:
            path (str): Full node path (e.g., "Device/Subsystem/Machine Status/Temperature Value")
            
        Returns:
            str: Normalized field name (e.g., "temperature")
        """
        # Step 1: Extract the last part of the path as the base field name
        # Example: "Device/Subsystem/Machine Status/Temperature Value" -> "Temperature Value"
        last_segment = path.split('/')[-1] or 'node'
        log.debug(f"Extracted last segment from path '{path}': '{last_segment}'")
        
        # Step 2: Normalize - replace non-alphanumerics with underscore, trim underscores, lowercase
        # Example: "Temperature Value" -> "temperature_value"
        field_name = re.sub(r'[^0-9A-Za-z]+', '_', last_segment).strip('_').lower()
        log.debug(f"After regex normalization: '{last_segment}' -> '{field_name}'")
        
        # Step 3: Remove common prefixes/suffixes to make field names cleaner
        # This helps avoid redundant words that don't add meaning
        original_field_name = field_name
        field_name = field_name.replace('machine_', '').replace('_value', '').replace('_current', '')
        if original_field_name != field_name:
            log.debug(f"After prefix/suffix removal: '{original_field_name}' -> '{field_name}'")
        
        # Step 4: Handle edge case where field name becomes empty after cleaning
        if not field_name:
            field_name = 'node'
            log.debug(f"Field name was empty after cleaning, defaulting to 'node'")
        
        return field_name
    
    @staticmethod
    def resolve_field_name_collisions(selected_nodes, field_name, path, node_id):
        """
        Resolve field name collisions by appending numeric suffixes.
        
        When multiple nodes normalize to the same field name, this function
        ensures uniqueness by appending _1, _2, etc. to subsequent occurrences.
        
        Args:
            selected_nodes (dict): Dictionary of already selected field names
            field_name (str): Proposed field name
            path (str): Original node path (for logging)
            node_id (str): Node ID (for logging)
            
        Returns:
            str: Unique field name that doesn't conflict with existing ones
        """
        base_field_name = field_name
        suffix = 1
        
        # Keep incrementing suffix until we find a unique name
        while field_name in selected_nodes:
            field_name = f"{base_field_name}_{suffix}"
            suffix += 1
        
        # Log collision resolution if we had to modify the name
        if suffix > 1:
            log.debug(f"Name collision resolved: path '{path}' (node: {node_id}) assigned field name '{field_name}' (was '{base_field_name}')")
        
        return field_name
