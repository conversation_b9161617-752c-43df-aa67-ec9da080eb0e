"""OPC UA Node Browser"""
import logging

log = logging.getLogger(__name__)


class NodeBrowser:
    """<PERSON>les browsing and discovering OPC UA nodes."""
    
    def __init__(self, connection, max_depth=5):
        """
        Initialize the node browser.
        
        Args:
            connection: OPCUAConnection instance
            max_depth (int): Maximum recursion depth for node discovery
        """
        self.connection = connection
        self.max_depth = max_depth
        self.discovered_nodes = {}
    
    async def browse_all_nodes(self):
        """
        Browse all nodes starting from the Objects root.
        
        Returns:
            dict: Dictionary of discovered nodes
        """
        log.info("Starting node discovery...")
        root_node = self.connection.get_node("i=85")  # Objects root
        await self._recursive_browse(root_node)
        log.info(f"Discovery complete. Found {len(self.discovered_nodes)} data nodes.")
        return self.discovered_nodes
    
    async def _recursive_browse(self, node, depth=0, path=""):
        """
        Recursively browse the OPC UA node tree and collect node information.
        Only collects nodes from custom namespaces (ns > 0) to avoid system nodes.
        
        Args:
            node: The OPC UA node to browse
            depth (int): Current recursion depth
            path (str): Path to the current node for context
        """
        # Prevent infinite recursion
        if depth > self.max_depth:
            log.warning(f"Maximum depth ({self.max_depth}) reached at path: {path}")
            return
        
        try:
            browse_name = await node.read_browse_name()
            node_id = str(node)
            display_name = browse_name.Name
            namespace_index = node.nodeid.NamespaceIndex
            
            # Filter out system nodes (namespace 0 by default on OPC UA servers)
            if namespace_index == 0:
                log.debug(f"Skipping system node (ns=0): {display_name}")
                await self._browse_children(node, depth, path)
                return
            
            current_path = f"{path}/{display_name}" if path else display_name
            
            await self._try_read_node_value(node, node_id, display_name, current_path, namespace_index)
            
            await self._browse_children(node, depth, current_path)
                
        except Exception as e:
            log.warning(f"Error browsing node at depth {depth}: {e}")
    
    async def _try_read_node_value(self, node, node_id, display_name, current_path, namespace_index):
        """
        Try to read a node's value and store if successful.
        
        Args:
            node: OPC UA node
            node_id (str): Node ID string
            display_name (str): Node display name
            current_path (str): Current node path
            namespace_index (int): Namespace index
        """
        try:
            value = await node.read_value()
            data_type = await node.read_data_type()
            
            self.discovered_nodes[current_path] = {
                'node_id': node_id,
                'display_name': display_name,
                'value': value,
                'data_type': str(data_type),
                'path': current_path,
                'namespace': namespace_index
            }
            log.info(f"Found sensor node: {current_path} = {value} [ns={namespace_index}] -> {node_id}")
        except Exception:
            log.debug(f"Node {display_name} has no readable value (likely a container)")
    
    async def _browse_children(self, node, depth, path):
        """
        Browse child nodes.
        
        Args:
            node: Parent OPC UA node
            depth (int): Current depth
            path (str): Current path
        """
        try:
            child_nodes = await node.get_children()
            for child in child_nodes:
                await self._recursive_browse(child, depth + 1, path)
        except Exception as e:
            log.debug(f"Could not browse children: {e}")
