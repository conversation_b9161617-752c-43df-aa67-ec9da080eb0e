# Core async programming
asyncio

# OPC UA client library for industrial communication
asyncua>=1.0.0

# Cryptography library for certificate generation (required for OPC UA security)
cryptography>=3.0.0

# Modbus TCP client library for industrial communication
# pyModbusTCP==0.2.1

# InfluxDB client for time series data storage
influxdb-client>=1.36.0

# Configuration file handling
PyYAML>=6.0

# For datetime handling
python-dateutil>=2.8.0

# HTTP requests for power meter web interface
requests>=2.25.0

# Logging
logging

# Standard library dependencies that should be available
time
datetime
threading
struct
socket
platform
subprocess
base64
signal
pathlib
os
sys
