import yaml
import logging
import os
from pathlib import Path

from .config_validator import validate_config, ConfigValidationError

# Get a child logger from the central logger configured in main.py
log = logging.getLogger("manufacturing_monitor.config")

def load_yaml_config(path="config.yaml"):
    """
    Loads and parses a YAML configuration file (low-level function).

    This is a basic YAML loader that handles file I/O and parsing.
    For application startup, use load_and_validate_config() instead.

    Args:
        path (str): The file path to the YAML configuration file.
                    Defaults to "config.yaml".

    Returns:
        dict: A dictionary containing the parsed YAML configuration.

    Raises:
        FileNotFoundError: If the configuration file cannot be found at the given path.
        yaml.YAMLError: If the file contains invalid YAML.
        ValueError: If the configuration file is empty.
    """
    
    log.info(f"Attempting to load configuration from: {path}")
    try:
        with open(path, 'r') as f:
            config = yaml.safe_load(f)
            if not config:
                raise ValueError("Configuration file is empty or invalid.")
            log.info("Configuration loaded successfully.")
            return config
    except FileNotFoundError:
        log.critical(f"Configuration file not found at: {path}. Application cannot start.")
        # Re-raise the exception to stop the application
        raise
    except yaml.YAMLError as e:
        log.critical(f"Error parsing YAML file: {e}. Application cannot start.")
        raise
    except ValueError as e:
        log.critical(str(e))
        raise

def load_and_validate_config():
    """
    Load and validate the complete application configuration (high-level function).
    
    This function handles the full application startup configuration process:
    - Loads the YAML config file
    - Validates the configuration structure
    - Provides comprehensive error handling
    - Returns None on any failure for graceful error handling
    
    Returns:
        dict: Validated configuration dictionary if successful, None if failed
    """
    try:
        log.info("Loading application configuration...")
        
        # Check if config file exists
        config_path = "config.yaml"
        if not Path(config_path).exists():
            log.error(f"Configuration file '{config_path}' not found in current directory")
            log.info(f"Current working directory: {os.getcwd()}")
            log.info("Please ensure config.yaml exists in the working directory")
            return None
        
        # Load configuration
        config = load_yaml_config(config_path)
        
        # Validate essential configuration sections
        try:
            validate_config(config)
            log.info("Configuration loaded and validated successfully")
            return config
        except ConfigValidationError as e:
            log.error(f"Configuration validation failed: {e}")
            return None
            
    except FileNotFoundError as e:
        log.critical(f"Configuration file not found: {e}")
        log.critical("Application cannot start without configuration file")
        return None
    except Exception as e:
        log.critical(f"Failed to load configuration: {e}")
        log.critical("Application cannot start with invalid configuration")
        return None