import logging
import os
from pathlib import Path
from datetime import datetime

from logger import setup_logger

# Global logger will be configured after setup_logger is called
log = None

def initialize_logging():
    """
    Initialize the application with proper logging and configuration setup.
    
    Returns:
        tuple: (logger, config) if successful, (None, None) if failed
    """
    global log
    
    try:
        print("Initializing Manufacturing Monitor...")
        print("Setting up logging system...")
        
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        print(f"Logs directory: {logs_dir.absolute()}")
        
        # Generate timestamped log file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"manufacturing_monitor_{timestamp}.log"
        
        # Determine log level from environment or default to INFO
        log_level_str = os.getenv('LOG_LEVEL', 'INFO').upper()
        # log_level = getattr(logging, log_level_str, logging.INFO)
        log_level = logging.DEBUG
        
        # Setup the logger with timestamped log file in logs directory
        log_file_path = logs_dir / log_filename
        logger = setup_logger(log_level=log_level, log_file=str(log_file_path))
        
        log = logging.getLogger("manufacturing_monitor.main")
        log.info("="*60)
        log.info("Manufacturing Monitor Starting Up")
        log.info("="*60)
        log.info(f"Log level set to: DEBUG") # {log_level_str}")
        log.info(f"Log file location: {log_file_path.absolute()}")
        log.info("Logging system initialized successfully")
        
        # Config will be loaded separately
        return logger, None
        
    except Exception as e:
        print(f"CRITICAL: Failed to initialize logging system: {e}")
        print("Application cannot continue without logging. Exiting.")
        return None, None